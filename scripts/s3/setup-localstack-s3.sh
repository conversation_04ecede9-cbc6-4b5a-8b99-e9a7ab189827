#!/bin/bash

# Setup LocalStack for local S3 development
set -e

echo "🚀 Setting up LocalStack for local S3 development..."

# Check if docker-compose is running
if ! docker-compose ps | grep -q "localstack"; then
    echo "Starting LocalStack..."
    docker-compose up -d localstack
else
    echo "LocalStack is already running"
fi

# Wait for LocalStack to be ready
echo "⏳ Waiting for LocalStack to be ready..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    if curl -s http://localhost:4567/_localstack/health > /dev/null; then
        echo "✅ LocalStack is ready!"
        break
    fi
    
    attempt=$((attempt + 1))
    echo "Attempt $attempt/$max_attempts - LocalStack not ready yet..."
    sleep 2
done

if [ $attempt -eq $max_attempts ]; then
    echo "❌ LocalStack failed to start within 60 seconds"
    exit 1
fi

# Create S3 bucket
echo "📦 Creating S3 bucket..."
aws --endpoint-url=http://localhost:4567 s3 mb s3://drumkit-local

if [ $? -eq 0 ]; then
    echo "✅ S3 bucket 'drumkit-local' created successfully"
else
    echo "⚠️  Bucket might already exist (this is OK)"
fi

# List buckets to verify
echo "📋 Available S3 buckets:"
aws --endpoint-url=http://localhost:4567 s3 ls

echo ""
echo "🎉 LocalStack setup complete!"
echo ""
echo "To test your pipeline:"
echo "1. Start your services: docker-compose up processor outlook_ingestion"
echo "2. Send an email through outlook_ingestion"
echo "3. Check S3 objects: aws --endpoint-url=http://localhost:4566 s3 ls s3://drumkit-local --recursive"
echo ""
echo "Environment variables for your services:"
echo "AWS_ACCESS_KEY_ID=test"
echo "AWS_SECRET_ACCESS_KEY=test"
echo "AWS_DEFAULT_REGION=us-east-1"
echo "AWS_ENDPOINT_URL=http://localstack:4566"
echo "S3_BUCKET_NAME=drumkit-local" 