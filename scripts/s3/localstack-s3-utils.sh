#!/bin/bash

# LocalStack S3 utility functions
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=us-east-1
export LOCALSTACK_ENDPOINT="http://localhost:4567"

# List all buckets
list_buckets() {
    aws --endpoint-url=$LOCALSTACK_ENDPOINT s3 ls
}

# List objects in a bucket
list_objects() {
    local bucket=${1:-drumkit-local}
    aws --endpoint-url=$LOCALSTACK_ENDPOINT s3 ls s3://$bucket --recursive
}

# Download an object
download_object() {
    local bucket=${1:-drumkit-local}
    local key=$2
    local output=${3:-./downloaded_file}
    
    if [ -z "$key" ]; then
        echo "Usage: download_object <bucket> <key> [output_file]"
        return 1
    fi
    
    aws --endpoint-url=$LOCALSTACK_ENDPOINT s3 cp s3://$bucket/$key $output
}

# Delete an object
delete_object() {
    local bucket=${1:-drumkit-local}
    local key=$2
    
    if [ -z "$key" ]; then
        echo "Usage: delete_object <bucket> <key>"
        return 1
    fi
    
    aws --endpoint-url=$LOCALSTACK_ENDPOINT s3 rm s3://$bucket/$key
}

# Clear all objects in bucket
clear_bucket() {
    local bucket=${1:-drumkit-local}
    aws --endpoint-url=$LOCALSTACK_ENDPOINT s3 rm s3://$bucket --recursive
}

# Show bucket stats
bucket_stats() {
    local bucket=${1:-drumkit-local}
    echo "Bucket: $bucket"
    echo "Objects:"
    aws --endpoint-url=$LOCALSTACK_ENDPOINT s3 ls s3://$bucket --recursive --human-readable --summarize
}

# Main function
case "${1:-help}" in
    "list")
        list_buckets
        ;;
    "objects")
        list_objects $2
        ;;
    "download")
        download_object $2 $3 $4
        ;;
    "delete")
        delete_object $2 $3
        ;;
    "clear")
        clear_bucket $2
        ;;
    "stats")
        bucket_stats $2
        ;;
    "help"|*)
        echo "LocalStack S3 Utilities"
        echo ""
        echo "Usage: source scripts/localstack-utils.sh"
        echo "Then use:"
        echo "  list_buckets          - List all buckets"
        echo "  list_objects [bucket] - List objects in bucket"
        echo "  download_object <bucket> <key> [output] - Download object"
        echo "  delete_object <bucket> <key> - Delete object"
        echo "  clear_bucket [bucket] - Clear all objects"
        echo "  bucket_stats [bucket] - Show bucket statistics"
        echo ""
        echo "Or run directly:"
        echo "  ./scripts/localstack-utils.sh list"
        echo "  ./scripts/localstack-utils.sh objects"
        echo "  ./scripts/localstack-utils.sh stats"
        ;;
esac 