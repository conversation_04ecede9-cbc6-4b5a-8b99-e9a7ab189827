package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/hardfinhq/go-date"
	"gorm.io/gorm"
)

// Enables handling diff types of IDs (IDType should be the filter that the TMS' API defines)
type TMSFreightIDs struct {
	IDType, ID string
}

// Represents a value and its unit of measurement, such as
type ValueUnit struct {
	Val  float32 `json:"val" jsonschema:"required"`
	Unit string  `json:"unit" jsonschema:"required"`
}

const (
	// Weight
	LbsUnit = "lbs"
	KgUnit  = "kg"

	// Distance
	MilesUnit = "miles"
	KmUnit    = "km"
)

type WebhookLoadBody struct {
	LoadCoreInfo
	ExternalTMSLoadID string `json:"externalTMSLoadID"`
	FreightLoadID     string `json:"freightLoadID"`
}

type SearchLoadsQuery struct {
	// If TMS's IDs are numeric and sequentially incrementing, limit search to IDs greater than this
	FromFreightTrackingID string `json:"oldestFreightTrackingID"`
	// If both a FromFreightTrackingID and FromDate are provided, FromDate is used
	FromDate      NullTime `json:"fromDate"`
	ToDate        NullTime `json:"toDate"`
	TransportType string   `json:"transportType"`
	Pickup        Address  `json:"pickup"`
	Dropoff       Address  `json:"dropoff"`
	Status        string   `json:"status"`
}

// LoadMode is an enum for load modes (TL, LTL)
type LoadMode string

const (
	TLMode                LoadMode = "TL"
	LTLMode               LoadMode = "LTL"
	RefrigeratedMode      LoadMode = "Refrigerated"
	EnterpriseMode        LoadMode = "Enterprise"
	RailMode              LoadMode = "Rail"
	DrayageMode           LoadMode = "Drayage"
	OverDimensionalMode   LoadMode = "Over Dimensional"
	FlatbedMode           LoadMode = "Flatbed"
	DummyLoadMode         LoadMode = "Dummy Load"
	TruckOrderNotUsedMode LoadMode = "Truck Order Not Used"
	DryVanMode            LoadMode = "Dry Van"
	DumpMode              LoadMode = "Dump"
	AirMode               LoadMode = "Air"
	SpotLoadMode          LoadMode = "Spot Load"
	BoxTruckMode          LoadMode = "Box Truck"
	PartialShipmentMode   LoadMode = "Partial Shipment"
	FlatbedHotshotMode    LoadMode = "Flatbed - HOTSHOT"
	PowerOnlyMode         LoadMode = "Power Only"
	BackupCommitmentMode  LoadMode = "Backup Commitment"
	DrayageContainerMode  LoadMode = "Drayage / Container"
	PrimaryCommitmentMode LoadMode = "Primary Commitment"

	// GlobalTranz Load Modes
	NoneMode    LoadMode = "None"
	PartialMode LoadMode = "Partial"
	BothMode    LoadMode = "Both" // Both Full and Partial
	VolumeMode  LoadMode = "Volume"
)

func ListLoadModes() []LoadMode {
	return []LoadMode{
		TLMode,
		LTLMode,
		RefrigeratedMode,
		EnterpriseMode,
		RailMode,
		DrayageMode,
		OverDimensionalMode,
		FlatbedMode,
		DummyLoadMode,
		TruckOrderNotUsedMode,
		DryVanMode,
		DumpMode,
		AirMode,
		SpotLoadMode,
		BoxTruckMode,
		PartialShipmentMode,
		FlatbedHotshotMode,
		PowerOnlyMode,
		BackupCommitmentMode,
		DrayageContainerMode,
		PrimaryCommitmentMode,
	}
}

// StringToLoadMode converts a string representation of load mode (e.g., "FTL", "LTL")
// to the standardized LoadMode enum, handling common variations.
// TODO: Each integration should have its own mapping of TMS load modes to LoadMode
func StringToLoadMode(input string) LoadMode {
	normalized := strings.ToUpper(strings.TrimSpace(input))

	switch normalized {
	case "T", "TL", "FTL", "TRUCKLOAD":
		return TLMode
	case "LTL":
		return LTLMode
	case "REFRIGERATED":
		return RefrigeratedMode
	case "ENTERPRISE":
		return EnterpriseMode
	case "RAIL":
		return RailMode
	case "DRAYAGE", "IMDL":
		return DrayageMode
	case "DRAYAGE / CONTAINER":
		return DrayageContainerMode
	case "OVER DIMENSIONAL":
		return OverDimensionalMode
	case "FLATBED":
		return FlatbedMode
	case "FLATBED - HOTSHOT":
		return FlatbedHotshotMode
	case "DUMMY LOAD":
		return DummyLoadMode
	case "TRUCK ORDER NOT USED":
		return TruckOrderNotUsedMode
	case "DRY VAN", "VAN":
		return DryVanMode
	case "DUMP":
		return DumpMode
	case "AIR":
		return AirMode
	case "SPOT LOAD":
		return SpotLoadMode
	case "BOX TRUCK":
		return BoxTruckMode
	case "PARTIAL SHIPMENT":
		return PartialShipmentMode
	case "POWER ONLY":
		return PowerOnlyMode
	case "BACKUP COMMITMENT":
		return BackupCommitmentMode
	case "P", "PRIMARY COMMITMENT":
		return PrimaryCommitmentMode
	default:
		return ""
	}
}

type LoadCoreInfo struct {
	Status           string   `json:"status"`
	Mode             LoadMode `json:"mode"`                // TL (truckload), LTL (Less-Than-Truckload) only
	MoreThanTwoStops bool     `json:"moreThanTwoStops"`    // LTL load does not necessarily have more than 2 stops
	PONums           string   `gorm:"index" json:"poNums"` // Purchase Order #s
	Operator         string   `json:"operator"`            // Operator from brokerage team assigned to manage the load

	// Bill of Lading, Load Numbers, and other reference numbers
	AdditionalReferences AdditionalReferences `gorm:"type:JSONB" json:"additionalReferences"`

	Customer Customer `gorm:"embedded;embeddedPrefix:customer_" json:"customer"`
	BillTo   BillTo   `gorm:"embedded;embeddedPrefix:billto_" json:"billTo"`
	RateData RateData `gorm:"embedded;embeddedPrefix:ratedata_" json:"rateData"`
	// NOTE: Pickup and Consignee fields are being transitioned to the Stops table.
	// This is part of a larger effort to support multiple stops and provide
	// a more flexible data model for complex shipping routes.
	//
	// Future:
	// - Stops []Stop `gorm:"foreignKey:LoadID"`
	// - Each stop will have a StopType (pickup/dropoff) and StopNumber for ordering
	// - This will allow for more than two stops per load
	//
	// For now, these fields are kept for backwards compatibility but will be
	// deprecated once the transition to Stops is complete.
	Stops []Stop `gorm:"foreignKey:LoadID;references:ID" json:"stops"`
	// Pickup aka Origin, Shipper
	Pickup Pickup `gorm:"embedded;embeddedPrefix:pickup_" json:"pickup"`
	// Consignee aka Delivery, Dropoff, Receiver
	Consignee Consignee `gorm:"embedded;embeddedPrefix:consignee_" json:"consignee"`
	// Carrier is the trucking company shipping the goods from pickup to dropoff
	Carrier        Carrier        `gorm:"embedded;embeddedPrefix:carrier_" json:"carrier"`
	Specifications Specifications `gorm:"embedded;embeddedPrefix:specifications_" json:"specifications"`
	Notes          Notes          `gorm:"type:JSONB" json:"notes"`

	// Date-only fields for pickup and dropoff (day, month, year)
	// may change over the lifetime of load as dates are updated under Pickup/Consignee and Carrier
	PickupDate  date.Date `json:"-" gorm:"type:date;default:NULL"`
	DropoffDate date.Date `json:"-" gorm:"type:date;default:NULL"`
}

type Load struct {
	gorm.Model
	LoadCoreInfo

	//nolint:lll
	ServiceID uint    `gorm:"uniqueIndex:idx_freight_id_service;index:idx_service_pickup_warehouse,priority:1;index:idx_service_dropoff_warehouse,priority:1" json:"serviceID"`
	Service   Service `json:"-"`
	TMSID     uint    `json:"tmsID"`
	//nolint:revive
	TMS Integration `json:"-" validate:"omitempty,dive"` // Integration must be of type = 'tms'

	Emails          []Email          `gorm:"many2many:email_loads;" json:"-"`
	GeneratedEmails []GeneratedEmail `gorm:"many2many:generated_email_loads;" json:"-"`
	// Boolean indicating if this is a placeholder load due to a transient error during processing pipeline.
	// When user opens the associated email, API will try to get from TMS again and upsert will reset to false.
	IsPlaceholder bool `json:"isPlaceholder"`
	// TMS's UUID for the load/shipment
	ExternalTMSID string `gorm:"index" json:"externalTMSID"`
	// TMS's/customer's client-facing ID for the load (e.g. NFI/Aljex = "PRO", Turvo = "customId")
	FreightTrackingID string `gorm:"uniqueIndex:idx_freight_id_service;index" json:"freightTrackingID"`

	Commodities      []Commodity `json:"commodities"`
	DeclaredValueUSD float32     `json:"declaredValueUSD"`

	// Part of composite index with ServiceID (ServiceID first)
	PickupWarehouseID uint `gorm:"default:NULL;index:idx_service_pickup_warehouse,priority:2" json:"pickupWarehouseID"`
	//nolint:revive
	PickupWarehouse Warehouse `json:"-" validate:"omitempty,dive"`
	//nolint:lll // Part of composite index with ServiceID (ServiceID first)
	DropoffWarehouseID uint `gorm:"default:NULL;index:idx_service_dropoff_warehouse,priority:2" json:"dropoffWarehouseID"`
	//nolint:revive
	DropoffWarehouse Warehouse `json:"-" validate:"omitempty,dive"`
}

type Customer struct {
	CompanyCoreInfo
	RefNumber string `gorm:"index:idx_customer_ref" json:"refNumber"`
}

type BillTo struct {
	CompanyCoreInfo
}

//nolint:lll
type Pickup struct {
	CompanyCoreInfo
	// Note this is different from CompanyCoreInfo.ExternalTMSID as that's the UUID for the *location*
	// ExternalTMSStopID is the UUID for this stop in the array of stops for this shipment. Primarily for Relay.
	ExternalTMSStopID    string               `json:"externalTMSStopID"`
	BusinessHours        string               `json:"businessHours"`
	RefNumber            string               `json:"refNumber"`
	AdditionalReferences AdditionalReferences `gorm:"type:JSONB" json:"additionalReferences"`
	ReadyTime            NullTime             `json:"readyTime"`
	ApptNum              string               `json:"apptNum"`
	ApptRequired         bool                 `json:"apptRequired"`
	ApptConfirmed        bool                 `json:"apptConfirmed"`
	ApptType             string               `json:"apptType"` // e.g. appointment, FCFS (first-come, first-serve), drop trailer, etc
	ApptStartTime        NullTime             `json:"apptStartTime"`
	ApptEndTime          NullTime             `json:"apptEndTime"` // Optional, TMS may not provide this
	ApptNote             string               `json:"apptNote"`
	Timezone             string               `json:"timezone"`               // IANA Timezone (e.g. "America/New_York")
	ZipPrefix            string               `json:"zipPrefix" gorm:"index"` // First 3 digits of zipcode
}

//nolint:lll
type Consignee struct {
	CompanyCoreInfo
	// Note this is different from CompanyCoreInfo.ExternalTMSID as that's the UUID for the *location*
	// ExternalTMSStopID is the UUID for this stop in the array of stops for this shipment. Primarily for Relay.
	ExternalTMSStopID    string               `json:"externalTMSStopID"` // Used to track location id in Turvo as well
	BusinessHours        string               `json:"businessHours"`
	RefNumber            string               `json:"refNumber"`
	AdditionalReferences AdditionalReferences `gorm:"type:JSONB" json:"additionalReferences"`
	MustDeliver          NullTime             `json:"mustDeliver"` // Date that delivery has to happen by
	ApptNum              string               `json:"apptNum"`
	ApptRequired         bool                 `json:"apptRequired"`
	ApptConfirmed        bool                 `json:"apptConfirmed"`
	ApptType             string               `json:"apptType"` // e.g. appointment, FCFS (first-come, first-serve), drop trailer, etc
	ApptStartTime        NullTime             `json:"apptStartTime"`
	ApptEndTime          NullTime             `json:"apptEndTime"` // Optional, TMS may not provide this
	ApptNote             string               `json:"apptNote"`
	Timezone             string               `json:"timezone"`               // IANA Timezone (e.g. "America/New_York")
	ZipPrefix            string               `json:"zipPrefix" gorm:"index"` // First 3 digits of zipcode
}

// The jsonschema fields are for OpenAI schema generation.
//
//nolint:lll
type CompanyCoreInfo struct {
	ExternalTMSID string `json:"externalTMSID" jsonschema_description:"Should be set to an empty string."`
	Name          string `json:"name" jsonschema_description:"Company or facility name (do not include address in this field)"`
	AddressLine1  string `json:"addressLine1" jsonschema_description:"Street address"`
	AddressLine2  string `json:"addressLine2" jsonschema_description:"Suite, unit, etc."`
	City          string `json:"city" jsonschema_description:"City name"`
	State         string `json:"state" jsonschema_description:"State code"`
	Zipcode       string `json:"zipCode" jsonschema_description:"Postal code"`
	Country       string `json:"country" jsonschema_description:"Country code (e.g., 'US')"`
	Contact       string `json:"contact" jsonschema_description:"Contact person name"`
	Phone         string `json:"phone" jsonschema_description:"Phone number (formatted with hyphens: ************)"`
	Email         string `json:"email" jsonschema_description:"Email address"`
}

type RateData struct {
	// Metadata
	CollectionMethod    string  `json:"collectionMethod"`    // e.g. Prepaid, Collect, Third-Party,
	RevenueCode         string  `json:"revenueCode"`         // Department/team in brokerage receiving the revenue
	Salesperson1        string  `json:"salesperson1"`        // Salesperson ID - tmsUser external_tms_id
	Salesperson1Percent float32 `json:"salesperson1Percent"` // Salesperson percentage - 0 - 100

	// ------ Customer rate info ------
	CustomerRateType string `json:"customerRateType"` // e.g. FlatRate, All In, Hourly, Mileage
	// CustomerNumHours  float32 `json:"customerNumHours"` // For Hourly rate type
	// CustomerLHRateUSD float32 `json:"customerLHRateUSD"`

	// LineHaulCharge = NumUnits * Rate. This is the base charge of transporting goods for the customer.
	// There may be additional charges on top of this base charge (e.g. fuel surcharge, special equipment, etc).
	// ValueUnit.Unit is currency (e.g. USD).
	CustomerLineHaulCharge ValueUnit `json:"customerLineHaulCharge"`

	// e.g. If RateType = distance and RateNumUnits = 10, then 10 miles. If type = hourly, then 10 = 10 hours.
	// If type = flat rate, then NumUnits is 1 (implicitly or explicitly)
	CustomerRateNumUnits float32 `json:"customerRateNumUnits"`

	// The per unit rate. If CustomerLineHaulCharge = $100, type = Mileage, and CustomerRateNumUnits = 10,
	// then the CustomerLineHaulRate is $100/10 = $10 / mile.
	// If type = hourly, then the rate is $100 / 10 = $10 / hour.
	CustomerLineHaulRate float32 `json:"customerLineHaulRate"`

	// If RateType = distance, then Unit = miles or km. If type = time, then Unit = hours, minutes, etc.
	// If type = hourly, then Unit = hours. If type = percentage, then Unit = percent.
	CustomerLineHaulUnit string `json:"customerLineHaulUnit"`

	// Accessorial charges, e.g. Drayage Fee, Loading Fee, etc.
	// NOTE: Depending on TMS (e.g. Turvo), this array may include linehaul charge and fuel surcharge
	CustomerLineItems LineItems `gorm:"type:JSONB" json:"customerLineItems"`

	// LineHaulCharge + Fuel Surcharge + LineItems = CustomerTotalCharge
	CustomerTotalCharge ValueUnit `json:"customerTotalCharge"` // ValueUnit.Unit is currency (e.g. USD).

	FSCPercent  float32 `json:"fscPercent"`  // Fuel Surcharge Percentage, 0 - 100
	FSCPerMile  float32 `json:"fscPerMile"`  // Fuel Surcharge Per Mile
	FSCFlatRate float32 `json:"fscFlatRate"` // Fuel Surcharge Flat Rate

	// ------ Carrier rate info ------
	CarrierRateType string `json:"carrierRateType"` // e.g. FlatRate, All In, Hourly, Mileage
	// CarrierLHRateUSD float32 `json:"carrierLHRateUSD"`

	// LineHaulCharge = NumUnits * Rate. This is the base cost of transporting goods for the carrier.
	// There may be additional charges on top of this base charge (e.g. loading fees, warehouse delay fees, etc).
	// ValueUnit.Unit is currency (e.g. USD).
	CarrierLineHaulCharge ValueUnit `json:"carrierLineHaulCharge"`

	// e.g. If RateType = distance and RateNumUnits = 10, then 10 miles. If type = hourly, then 10 = 10 hours.
	// If type = flat rate, then NumUnits is 1 (implicitly or explicitly)
	CarrierRateNumUnits float32 `json:"carrierRateNumUnits"`

	// The per unit rate. If CarrierLineHaulCharge = $100, type = Mileage, and CarrierRateNumUnits = 10,
	// then the CarrierLineHaulRate is $100/10 = $10 / mile.
	// If type = hourly, then the rate is $100 / 10 = $10 / hour.
	CarrierLineHaulRate float32 `json:"carrierLineHaulRate"`

	// If RateType = distance, then Unit = miles or km. If type = time, then Unit = hours, minutes, etc.
	// If type = hourly, then Unit = hours. If type = percentage, then Unit = percent.
	CarrierLineHaulUnit string `json:"carrierLineHaulUnit"`

	// Accessorial charges, e.g. Drayage Fee, Loading Fee, etc.
	// LineHaulCharge + Fuel Surcharge + other LineItems = CarrierTotalCharge
	CarrierLineItems LineItems `gorm:"type:JSONB" json:"carrierLineItems"`

	// LineHaulCharge + Fuel Surcharge + other LineItems = CarrierTotalCharge
	// ValueUnit not used so loads can be queried by column for Lane History feature
	CarrierCost         *float32 `json:"carrierCost"`         // Nullable as loads can exist with no carrier cost
	CarrierCostCurrency string   `json:"carrierCostCurrency"` // Currency of CarrierCost (e.g. USD, CAD)

	CarrierMaxRate float32 `json:"carrierMaxRate"`

	// ------ Profit info ------
	NetProfitUSD  float32 `json:"netProfitUSD"`
	ProfitPercent float32 `json:"profitPercent"` // 0 - 100

	// Deprecated: Use CarrierCost and CarrierCostCurrency instead
	CarrierTotalCost ValueUnit `json:"carrierTotalCost"` // TODO: remove this field
}

type Carrier struct {
	ExternalTMSID string `json:"externalTMSID"`
	MovementID    string `json:"movementID"` // Primarily for Mcleod
	Name          string `json:"name"`
	MCNumber      string `json:"mcNumber"`
	DOTNumber     string `json:"dotNumber"`
	SealNumber    string `json:"sealNumber"`
	SCAC          string `json:"scac"`
	Phone         string `json:"phone"`
	Email         string `json:"email"`
	Notes         string `json:"notes"`

	FirstDriverName   string `json:"firstDriverName"`
	FirstDriverPhone  string `json:"firstDriverPhone"`
	SecondDriverName  string `json:"secondDriverName"`
	SecondDriverPhone string `json:"secondDriverPhone"`
	Dispatcher        string `json:"dispatcher"`
	DispatchSource    string `json:"dispatchSource"` // For Relay; e.g. dispatcher, driver, shipper/receiver, web
	DispatchCity      string `json:"dispatchCity"`
	DispatchState     string `json:"dispatchState"`
	// NOTE: from TMS, NOT Axle's ID for the truck
	ExternalTMSTruckID string `json:"truckNumber"`
	// NOTE: from TMS, NOT Axle's ID for the trailer
	ExternalTMSTrailerID string `json:"trailerNumber"`

	RateConfirmationSent     bool     `json:"rateConfirmationSent"`
	ConfirmationSentTime     NullTime `json:"confirmationSentTime"`
	ConfirmationReceivedTime NullTime `json:"confirmationReceivedTime"`
	DispatchedTime           NullTime `json:"dispatchedTime"`
	ExpectedPickupTime       NullTime `json:"expectedPickupTime"`
	PickupStart              NullTime `json:"pickupStart"`
	PickupEnd                NullTime `json:"pickupEnd"` // i.e. loaded time
	ExpectedDeliveryTime     NullTime `json:"expectedDeliveryTime"`
	DeliveryStart            NullTime `json:"deliveryStart"`
	DeliveryEnd              NullTime `json:"deliveryEnd"`
	SignedBy                 string   `json:"signedBy"`
	EquipmentName            string   `json:"equipmentName"` // e.g. VAN
}

type Specifications struct {
	// OrderType is different from Mode which is the type of load (e.g. TL, LTL)
	// OrderType is primarily for McleodEnterprise and enums vary by tenant
	OrderType           string         `json:"orderType"`
	PlanningComment     string         `json:"planningComment"`
	PalletsRequired     bool           `json:"palletsRequired"`
	TotalInPalletCount  int            `json:"totalInPalletCount"` // this is pallet count
	TotalOutPalletCount int            `json:"totalOutPalletCount"`
	TotalPieces         ValueUnit      `json:"totalPieces"`     // this is commodity count
	TotalPiecesType     string         `json:"totalPiecesType"` // e.g. "pallets", "pieces", "boxes", "other"
	Commodities         string         `json:"commodities"`     // Use []Commodities for details
	NumCommodities      int            `json:"numCommodities"`
	TotalWeight         ValueUnit      `json:"totalWeight"`
	TotalVolume         ValueUnit      `json:"totalVolume"`
	NetWeight           ValueUnit      `json:"netWeight"`
	BillableWeight      ValueUnit      `json:"billableWeight"`
	TotalDistance       ValueUnit      `json:"totalDistance" gorm:"type:JSONB"`
	ServiceType         string         `json:"serviceType"`
	TransportType       string         `json:"transportType"`     // e.g. options from the TMS
	TransportTypeEnum   *TransportType `json:"transportTypeEnum"` // e.g. enums in Drumkit Quote Request
	TransportSize       string         `json:"transportSize"`
	MinTempFahrenheit   float32        `json:"minTempFahrenheit"`
	MaxTempFahrenheit   float32        `json:"maxTempFahrenheit"`
	IsRefrigerated      bool           `json:"isRefrigerated"`
	LiftgatePickup      bool           `json:"liftgatePickup"`
	LiftgateDelivery    bool           `json:"liftgateDelivery"`
	InsidePickup        bool           `json:"insidePickup"`
	InsideDelivery      bool           `json:"insideDelivery"`
	Tarps               bool           `json:"tarps"`
	Oversized           bool           `json:"oversized"`
	Hazmat              bool           `json:"hazmat"`
	Straps              bool           `json:"straps"`
	Permits             bool           `json:"permits"`
	Escorts             bool           `json:"escorts"`
	Seal                bool           `json:"seal"`
	CustomBonded        bool           `json:"customBonded"`
	Labor               bool           `json:"labor"`
}

type Note struct {
	Note        string   `json:"note"`
	CreatedAt   NullTime `json:"createdAt"`
	UpdatedBy   string   `json:"updatedBy"`
	Source      string   `json:"source"`
	IsException *bool    `json:"isException"`
	IsOnTime    *bool    `json:"isOnTime"`
}

type LineItems []AccessorialCharge

// AccessorialCharge represents an additional charge/cost for a load, e.g. Drayage Fee, Loading Fee, etc.
// There can be multiple accessorial charges for a load.
// LineHaulCharge + Fuel Surcharge + LineItems = CustomerTotalCharge
// NOTE: Fuel Surcharge is not included here, but is handled separately.
type AccessorialCharge struct {
	Label          string  `json:"label"`          // Required
	UnitBasis      string  `json:"unitBasis"`      // e.g. flat, hour, mile, etc.
	Quantity       float32 `json:"quantity"`       //nolint:lll // if unit basis is flat, then quantity is 1, otherwise quantity is the number of units (miles, hours, weight, etc)
	RatePerUnitUSD float32 `json:"ratePerUnitUSD"` // e.g. $100 / hour, $15 / mile, etc.
	TotalChargeUSD float32 `json:"totalChargeUSD"` // i.e. Quantity * RatePerUnitUSD
	Note           string  `json:"note"`           // optional
}

// Helper function to safely get a UTC normalized date from a NullTime
func getNormalizedDateFromNullTime(nt NullTime) (date.Date, error) {
	// Treat zero or invalid times as missing; avoid saving 0000/0001 dates.
	if !nt.Valid || nt.Time.IsZero() {
		return date.Date{}, errors.New("invalid null time")
	}
	// Convert to UTC before extracting date components
	utcTime := nt.Time.UTC()
	d := date.Date{
		Year:  utcTime.Year(),
		Month: utcTime.Month(),
		Day:   utcTime.Day(),
	}
	return d, nil
}

// UpdateDates updates PickupDate and DropoffDate
func (l *Load) UpdateDates() error {
	var errs []error

	// Try pickup date from PickupStart first, then ReadyTime, then ApptStartTime
	if pickupDate, err := getNormalizedDateFromNullTime(l.Carrier.PickupStart); err == nil {
		l.PickupDate = pickupDate
	} else if pickupDate, err := getNormalizedDateFromNullTime(l.Pickup.ReadyTime); err == nil {
		l.PickupDate = pickupDate
	} else if pickupDate, err := getNormalizedDateFromNullTime(l.Pickup.ApptStartTime); err == nil {
		l.PickupDate = pickupDate
	} else {
		errs = append(errs, fmt.Errorf("failed to set pickup date: %w", err))
	}

	// Try dropoff date from DeliveryStart first, then MustDeliver, then ApptStartTime
	if dropoffDate, err := getNormalizedDateFromNullTime(l.Carrier.DeliveryStart); err == nil {
		l.DropoffDate = dropoffDate
	} else if dropoffDate, err := getNormalizedDateFromNullTime(l.Consignee.MustDeliver); err == nil {
		l.DropoffDate = dropoffDate
	} else if dropoffDate, err := getNormalizedDateFromNullTime(l.Consignee.ApptStartTime); err == nil {
		l.DropoffDate = dropoffDate
	} else {
		errs = append(errs, fmt.Errorf("failed to set dropoff date: %w", err))
	}

	if len(errs) > 0 {
		return errors.Join(errs...)
	}
	return nil
}

func (l Load) GetPickupDate() (time.Time, error) {
	if !l.PickupDate.IsZero() {
		t := time.Date(
			l.PickupDate.Year,
			l.PickupDate.Month,
			l.PickupDate.Day,
			12, 0, 0, 0,
			time.UTC,
		)
		return t, nil
	}

	if l.Pickup.ApptStartTime.Valid {
		return l.Pickup.ApptStartTime.Time, nil
	}
	if l.Pickup.ReadyTime.Valid {
		return l.Pickup.ReadyTime.Time, nil
	}
	return time.Time{}, errors.New("no pickup date found")
}

// Should only be called if CarrierCost is not nil. (Responsibility of caller)
// Currently used in LaneHistory which queries for loads where CarrierCost is not nil.
// TODO: can modify GetTotalCarrierCost interface to handle nil case at this access level
func (l Load) GetTotalCarrierCost() float32 {
	if l.RateData.CarrierCost != nil {
		return *l.RateData.CarrierCost
	}
	return 0 // expects float32, but loads may not have CarrierCost
}

func (l Load) GetTotalDistance() float32 {
	return l.Specifications.TotalDistance.Val
}

func (l Load) GetCarrierName() string {
	return l.Carrier.Name
}

var _ LaneHistoryRawDataAccessor = Load{}

// --------------- JSONB Types ---------------

var (
	_ sql.Scanner   = &LineItems{}
	_ driver.Valuer = &LineItems{}
)

func (a *LineItems) Scan(value any) error {
	if value == nil {
		*a = nil
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for LineItems: %T", value)
	}
	var accessorialCharges []AccessorialCharge
	if err := json.Unmarshal(val, &accessorialCharges); err != nil {
		return err
	}
	*a = accessorialCharges
	return nil
}

func (a LineItems) Value() (driver.Value, error) {
	if len(a) == 0 {
		return nil, nil
	}
	return json.Marshal(a)
}

type Notes []Note

// Implements sql.Scanner interface
func (n *Notes) Scan(value any) error {
	if value == nil {
		*n = nil
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for Notes: %T", value)
	}
	var notes []Note
	if err := json.Unmarshal(val, &notes); err != nil {
		return err
	}
	*n = notes
	return nil
}

// Implement driver.Valuer interface
func (n Notes) Value() (driver.Value, error) {
	if len(n) == 0 {
		return nil, nil
	}

	return json.Marshal(n)
}

var (
	_ sql.Scanner   = &Notes{}
	_ driver.Valuer = &Notes{}
)

func (v *ValueUnit) Scan(value any) error {
	if value == nil {
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for ValueUnit: %T", value)
	}
	var vu ValueUnit
	if err := json.Unmarshal(val, &vu); err != nil {
		return err
	}
	*v = vu
	return nil
}

// Implement driver.Valuer interface
func (v ValueUnit) Value() (driver.Value, error) {
	return json.Marshal(v)
}

var (
	_ sql.Scanner   = &ValueUnit{}
	_ driver.Valuer = &ValueUnit{}
)

type ChangeType int

const (
	TMSOperatorNoChange ChangeType = iota
	TMSOperatorAssignment
	TMSOperatorRemoval
	TMSOperatorUpdate
)

func DetectTMSOperatorChange(oldValue, newValue string) ChangeType {
	oldEmpty := strings.TrimSpace(oldValue) == ""
	newEmpty := strings.TrimSpace(newValue) == ""

	switch {
	case oldEmpty && newEmpty:
		// both are empty: no change
		return TMSOperatorNoChange
	case oldEmpty && !newEmpty:
		// old value is empty, new value is not empty: assignment
		return TMSOperatorAssignment
	case !oldEmpty && newEmpty:
		// old value is not empty, new value is empty: removal
		return TMSOperatorRemoval
	case !oldEmpty && !newEmpty && newValue == oldValue:
		// old and new value are not empty, they're equal: no change
		return TMSOperatorNoChange
	default:
		// both are not empty, and different: update
		return TMSOperatorUpdate
	}
}

func (l Load) IsEmpty() bool {
	return strings.TrimSpace(l.FreightTrackingID) == "" &&
		strings.TrimSpace(l.Status) == "" &&
		reflect.DeepEqual(l.RateData, RateData{}) &&
		reflect.DeepEqual(l.Customer, Customer{}) &&
		reflect.DeepEqual(l.BillTo, BillTo{}) &&
		reflect.DeepEqual(l.Pickup, Pickup{}) &&
		reflect.DeepEqual(l.Consignee, Consignee{}) &&
		reflect.DeepEqual(l.Carrier, Carrier{}) &&
		reflect.DeepEqual(l.Specifications, Specifications{}) &&
		len(l.Commodities) == 0
}

type LoadDiff struct {
	Load      []string
	BillTo    []string
	Carrier   []string
	Consignee []string
	Customer  []string
	Pickup    []string
	RateData  []string
}

func (d LoadDiff) IsEmpty() bool {
	if len(d.Load) > 0 {
		return false
	}

	return len(d.RateData) == 0 &&
		len(d.Customer) == 0 &&
		len(d.BillTo) == 0 &&
		len(d.Pickup) == 0 &&
		len(d.Consignee) == 0 &&
		len(d.Carrier) == 0
}

func (l Load) Diff(other Load) LoadDiff {
	var diff LoadDiff

	// Intentionally do not check Status as Aljex automatically updates it based on the inputs

	// FreightTrackingID should be case sensitive
	if l.FreightTrackingID != other.FreightTrackingID {
		diff.Load = append(diff.Load, "PRO #")
	}

	if !strings.EqualFold(l.Operator, other.Operator) {
		diff.Load = append(diff.Load, "Operator")
	}

	diff.BillTo = l.BillTo.Diff(other.BillTo)
	diff.Carrier = l.Carrier.Diff(other.Carrier)
	diff.Consignee = l.Consignee.Diff(other.Consignee)
	diff.Customer = l.Customer.Diff(other.Customer)
	diff.Pickup = l.Pickup.Diff(other.Pickup)
	diff.RateData = l.RateData.Diff(other.RateData)

	return diff
}

func (c CompanyCoreInfo) Diff(other CompanyCoreInfo) []string {
	var diffs []string

	if !strings.EqualFold(c.Name, other.Name) {
		diffs = append(diffs, "Name")
	}

	if !strings.EqualFold(c.AddressLine1, other.AddressLine1) {
		diffs = append(diffs, "Address Line1")
	}

	if !strings.EqualFold(c.AddressLine2, other.AddressLine2) {
		diffs = append(diffs, "Address Line2")
	}

	if !strings.EqualFold(c.City, other.City) {
		diffs = append(diffs, "City")
	}

	if !strings.EqualFold(c.State, other.State) {
		diffs = append(diffs, "State")
	}

	if !strings.EqualFold(c.Zipcode, other.Zipcode) {
		diffs = append(diffs, "Zip Code")
	}

	if !strings.EqualFold(c.Country, other.Country) {
		diffs = append(diffs, "Country")
	}

	if !strings.EqualFold(c.Contact, other.Contact) {
		diffs = append(diffs, "Contact")
	}

	if !strings.EqualFold(c.Phone, other.Phone) {
		diffs = append(diffs, "Phone")
	}

	if !strings.EqualFold(c.Email, other.Email) {
		diffs = append(diffs, "Email")
	}

	return diffs
}

func (b BillTo) Diff(other BillTo) []string {
	return b.CompanyCoreInfo.Diff(other.CompanyCoreInfo)
}

func (c Carrier) Diff(other Carrier) []string {
	var diffs []string

	if !strings.EqualFold(c.MCNumber, other.MCNumber) {
		diffs = append(diffs, "Mc #")
	}

	if !strings.EqualFold(c.DOTNumber, other.DOTNumber) {
		diffs = append(diffs, "Dot #")
	}

	if !strings.EqualFold(c.Name, other.Name) {
		diffs = append(diffs, "Name")
	}

	if !strings.EqualFold(c.Phone, other.Phone) {
		diffs = append(diffs, "Phone")
	}

	if !strings.EqualFold(c.Dispatcher, other.Dispatcher) {
		diffs = append(diffs, "Dispatcher")
	}

	if !strings.EqualFold(c.SealNumber, other.SealNumber) {
		diffs = append(diffs, "Seal #")
	}

	if !strings.EqualFold(c.SCAC, other.SCAC) {
		diffs = append(diffs, "Scac")
	}

	if !strings.EqualFold(c.FirstDriverName, other.FirstDriverName) {
		diffs = append(diffs, "First Driver Name")
	}

	if !strings.EqualFold(c.FirstDriverPhone, other.FirstDriverPhone) {
		diffs = append(diffs, "First Driver Phone")
	}

	if !strings.EqualFold(c.SecondDriverName, other.SecondDriverName) {
		diffs = append(diffs, "Second Driver Name")
	}

	if !strings.EqualFold(c.SecondDriverPhone, other.SecondDriverPhone) {
		diffs = append(diffs, "Second Driver Phone")
	}

	if !strings.EqualFold(c.Email, other.Email) {
		diffs = append(diffs, "Email")
	}

	if !strings.EqualFold(c.DispatchCity, other.DispatchCity) {
		diffs = append(diffs, "Dispatch City")
	}

	if !strings.EqualFold(c.DispatchState, other.DispatchState) {
		diffs = append(diffs, "Dispatch State")
	}

	if !strings.EqualFold(c.ExternalTMSTruckID, other.ExternalTMSTruckID) {
		diffs = append(diffs, "Truck #")
	}

	if !strings.EqualFold(c.ExternalTMSTrailerID, other.ExternalTMSTrailerID) {
		diffs = append(diffs, "Trailer #")
	}
	if !strings.EqualFold(c.Notes, other.Notes) {
		diffs = append(diffs, "Trailer #")
	}

	if !c.ConfirmationSentTime.Equal(other.ConfirmationSentTime) {
		diffs = append(diffs, "Confirmation Sent Time")
	}

	if !c.ConfirmationReceivedTime.Equal(other.ConfirmationReceivedTime) {
		diffs = append(diffs, "Confirmation Sent Time")
	}

	if !c.DispatchedTime.Equal(other.DispatchedTime) {
		diffs = append(diffs, "Dispatched Time")
	}

	if !c.ExpectedPickupTime.Equal(other.ExpectedPickupTime) {
		diffs = append(diffs, "Expected Pickup Time")
	}

	if !c.PickupStart.Equal(other.PickupStart) {
		diffs = append(diffs, "Pickup Start Time")
	}

	if !c.PickupEnd.Equal(other.PickupEnd) {
		diffs = append(diffs, "Pickup End Time")
	}

	if !c.ExpectedDeliveryTime.Equal(other.ExpectedDeliveryTime) {
		diffs = append(diffs, "Expected Delivery Time")
	}

	if !c.DeliveryStart.Equal(other.DeliveryStart) {
		diffs = append(diffs, "Delivery Start Time")
	}

	if !c.DeliveryEnd.Equal(other.DeliveryEnd) {
		diffs = append(diffs, "Delivery End Time")
	}

	if !strings.EqualFold(c.SignedBy, other.SignedBy) {
		diffs = append(diffs, "Signed By")
	}

	return diffs
}

func (c Consignee) Diff(other Consignee) []string {
	diffs := c.CompanyCoreInfo.Diff(other.CompanyCoreInfo)

	if !strings.EqualFold(c.BusinessHours, other.BusinessHours) {
		diffs = append(diffs, "Business Hours")
	}

	if !strings.EqualFold(c.RefNumber, other.RefNumber) {
		diffs = append(diffs, "Ref #")
	}

	if !c.MustDeliver.Equal(other.MustDeliver) {
		diffs = append(diffs, "Must Deliver")
	}

	if c.ApptType != other.ApptType {
		diffs = append(diffs, "Appt Type")
	}

	if !c.ApptStartTime.Equal(other.ApptStartTime) {
		diffs = append(diffs, "Appt Start Time")
	}

	if !c.ApptEndTime.Equal(other.ApptEndTime) {
		diffs = append(diffs, "Appt End Time")
	}

	if !strings.EqualFold(c.ApptNote, other.ApptNote) {
		diffs = append(diffs, "Appt Note")
	}

	if !strings.EqualFold(c.ZipPrefix, other.ZipPrefix) {
		diffs = append(diffs, "Zip Prefix")
	}

	return diffs
}

func (c Customer) Diff(other Customer) []string {

	diffs := c.CompanyCoreInfo.Diff(other.CompanyCoreInfo)

	if !strings.EqualFold(c.RefNumber, other.RefNumber) {
		diffs = append(diffs, "Ref #")
	}

	return diffs
}

func (p Pickup) Diff(other Pickup) []string {
	diffs := p.CompanyCoreInfo.Diff(other.CompanyCoreInfo)

	if !strings.EqualFold(p.BusinessHours, other.BusinessHours) {
		diffs = append(diffs, "Business Hours")
	}

	if !strings.EqualFold(p.RefNumber, other.RefNumber) {
		diffs = append(diffs, "Ref #")
	}

	if !p.ReadyTime.Equal(other.ReadyTime) {
		diffs = append(diffs, "Ready Time")
	}

	if p.ApptType != other.ApptType {
		diffs = append(diffs, "Appt Type")
	}

	if !p.ApptStartTime.Equal(other.ApptStartTime) {
		diffs = append(diffs, "Appt Start Time")
	}

	if !p.ApptEndTime.Equal(other.ApptEndTime) {
		diffs = append(diffs, "Appt End Time")
	}

	if !strings.EqualFold(p.ApptNote, other.ApptNote) {
		diffs = append(diffs, "Appt Note")
	}

	if !strings.EqualFold(p.ZipPrefix, other.ZipPrefix) {
		diffs = append(diffs, "Zip Prefix")
	}

	return diffs
}

func (r RateData) Diff(other RateData) []string {
	var diffs []string

	// Customer rate info
	if !strings.EqualFold(r.CustomerRateType, other.CustomerRateType) {
		diffs = append(diffs, "Customer Rate Type")
	}

	if r.CustomerRateNumUnits != other.CustomerRateNumUnits {
		diffs = append(diffs, "Customer Rate Num Units")
	}

	if r.CustomerLineHaulRate != other.CustomerLineHaulRate {
		diffs = append(diffs, "Customer LH Rate")
	}

	if r.CustomerLineHaulCharge.Val != other.CustomerLineHaulCharge.Val ||
		!strings.EqualFold(r.CustomerLineHaulCharge.Unit, other.CustomerLineHaulCharge.Unit) {
		diffs = append(diffs, "Customer LH Charge")
	}

	if r.CustomerTotalCharge != other.CustomerTotalCharge {
		diffs = append(diffs, "Customer Total Charge")
	}

	// Carrier rate info
	if r.CarrierRateType != other.CarrierRateType {
		diffs = append(diffs, "Carrier Rate Type")
	}

	if r.CarrierRateNumUnits != other.CarrierRateNumUnits {
		diffs = append(diffs, "Carrier Rate Num Units")
	}

	if r.CarrierLineHaulRate != other.CarrierLineHaulRate {
		diffs = append(diffs, "Carrier LH Rate USD")
	}

	if (r.CarrierCost != nil) != (other.CarrierCost != nil) ||
		(r.CarrierCost != nil && other.CarrierCost != nil &&
			*r.CarrierCost != *other.CarrierCost) {
		diffs = append(diffs, "Carrier Cost")
	}

	if !strings.EqualFold(r.CarrierCostCurrency, other.CarrierCostCurrency) {
		diffs = append(diffs, "Carrier Cost Currency")
	}

	if r.CarrierMaxRate != other.CarrierMaxRate {
		diffs = append(diffs, "Carrier Max Rate")
	}

	// Not included because often re-calculated by the TMS so inevitably differs from the payload Drumkit submitted
	// if r.NetProfitUSD != other.NetProfitUSD {
	// 	diffs = append(diffs, "Net Profit USD")
	// }

	// if r.ProfitPercent != other.ProfitPercent {
	// 	diffs = append(diffs, "Profit Percent")
	// }

	if r.FSCPercent != other.FSCPercent {
		diffs = append(diffs, "FSC Percent")
	}

	if r.FSCPerMile != other.FSCPerMile {
		diffs = append(diffs, "FSC Per Mile")
	}

	return diffs
}

func (d LoadDiff) BuildErrMsg() string {
	nonUpdatedFields := d.Load

	if len(d.BillTo) > 0 {
		nonUpdatedFields = append(nonUpdatedFields, "Bill to: "+strings.Join(d.BillTo, ", Bill To:"))
	}

	if len(d.Carrier) > 0 {
		nonUpdatedFields = append(nonUpdatedFields, "Carrier: "+strings.Join(d.Carrier, ", "))
	}

	if len(d.Customer) > 0 {
		nonUpdatedFields = append(nonUpdatedFields, "Customer: "+strings.Join(d.Customer, ", "))
	}

	if len(d.Consignee) > 0 {
		nonUpdatedFields = append(nonUpdatedFields, "Drop off: "+strings.Join(d.Consignee, ", "))
	}

	if len(d.Pickup) > 0 {
		nonUpdatedFields = append(nonUpdatedFields, "Pick up: "+strings.Join(d.Pickup, ", "))
	}

	if len(d.RateData) > 0 {
		nonUpdatedFields = append(nonUpdatedFields, "Rates: "+strings.Join(d.RateData, ", "))
	}

	errMsg := "Unable to update fields: " + strings.Join(nonUpdatedFields, "; ")

	return errMsg
}

// LoadReferenceInfo is a minified struct that contains only the fields we need for the LLM
type LoadReferenceInfo struct {
	CustomerRefNumber  string `json:"customerRefNumber"`
	PoNums             string `json:"poNums"`
	PickupRefNumber    string `json:"pickupRefNumber"`
	ConsigneeRefNumber string `json:"consigneeRefNumber"`
}
