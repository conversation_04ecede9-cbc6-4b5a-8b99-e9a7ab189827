package models

import (
	"errors"

	"gorm.io/gorm"
)

type PromptScope string
type PromptExtractorName string

const (
	CustomerScope PromptScope = "tms_customer"
	TMSScope      PromptScope = "tms"

	PromptExtractorNameBasicInfo        PromptExtractorName = "basicInfo"
	PromptExtractorNameCustomer         PromptExtractorName = "customer"
	PromptExtractorNamePickup           PromptExtractorName = "pickup"
	PromptExtractorNameConsignee        PromptExtractorName = "consignee"
	PromptExtractorNameRateData         PromptExtractorName = "rateData"
	PromptExtractorNameSpecifications   PromptExtractorName = "specifications"
	PromptExtractNameCommoditiesAndRefs PromptExtractorName = "commoditiesRefs"
)

var AllLoadBuildingExtractors = []PromptExtractorName{
	PromptExtractorNameBasicInfo,
	PromptExtractorNameCustomer,
	PromptExtractorNamePickup,
	PromptExtractorNameConsignee,
	PromptExtractorNameRateData,
	PromptExtractorNameSpecifications,
	PromptExtractNameCommoditiesAndRefs,
}

type TMSCustomerPrompt struct {
	gorm.Model

	TMSIntegrationID uint        `gorm:"index" json:"tmsIntegrationID"`
	Integration      Integration `gorm:"foreignKey:TMSIntegrationID" json:"-"`

	//nolint:lll
	Scope         PromptScope  `gorm:"type:text;check:scope IN ('tms_customer', 'tms');default:'tms_customer'" json:"scope"`
	TMSCustomerID *uint        `gorm:"index" json:"tmsCustomerID"` // Nullable for TMS-wide prompts
	TMSCustomer   *TMSCustomer `gorm:"foreignKey:TMSCustomerID" json:"-"`
	Feature       string       `gorm:"type:text;default:'loadBuilding'" json:"feature"`

	ExtractorName string `gorm:"index" json:"extractorName"`
	PromptText    string `gorm:"type:text" json:"promptText"`
	IsActive      bool   `gorm:"default:true" json:"isActive"`
}

// Validate ensures the model follows business rules
func (tcp *TMSCustomerPrompt) Validate() error {
	if tcp.Scope == CustomerScope && tcp.TMSCustomerID == nil {
		return errors.New("tms_customer scope requires tms_customer_id")
	}
	if tcp.Scope == TMSScope && tcp.TMSCustomerID != nil {
		return errors.New("tms scope should not have tms_customer_id")
	}
	if tcp.ExtractorName == "" {
		return errors.New("extractor_name is required")
	}
	if tcp.Feature == "" {
		return errors.New("feature is required")
	}
	return nil
}

// BeforeSave validates the model before saving
func (tcp *TMSCustomerPrompt) BeforeSave(_ *gorm.DB) error {
	return tcp.Validate()
}

// BeforeCreate validates the model before creating
func (tcp *TMSCustomerPrompt) BeforeCreate(_ *gorm.DB) error {
	return tcp.Validate()
}

// BeforeUpdate validates the model before updating
func (tcp *TMSCustomerPrompt) BeforeUpdate(_ *gorm.DB) error {
	return tcp.Validate()
}

// IsCustomerSpecific returns true if this is a customer-specific prompt
func (tcp *TMSCustomerPrompt) IsCustomerSpecific() bool {
	return tcp.Scope == CustomerScope
}

// IsTMSWide returns true if this is a TMS-wide prompt
func (tcp *TMSCustomerPrompt) IsTMSWide() bool {
	return tcp.Scope == TMSScope
}
