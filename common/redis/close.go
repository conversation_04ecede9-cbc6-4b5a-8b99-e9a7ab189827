package redis

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// Close gracefully closes the Redis connection
func Close(ctx context.Context) error {
	if RDB == nil {
		log.Debug(ctx, "Redis client already closed or not initialized")
		return nil
	}

	if err := RDB.Close(); err != nil {
		log.Error(ctx, "failed to close Redis connection", zap.Error(err))
		return err
	}

	log.Info(ctx, "Redis connection closed successfully")
	RDB = nil
	return nil
}

// CloseWithTimeout closes the Redis connection with a timeout context
func CloseWithTimeout(ctx context.Context, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	return Close(ctx)
}
