package outlookhelpers

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient/mock"
	commonredis "github.com/drumkitai/drumkit/common/redis"
)

func TestShouldProcessMessage(t *testing.T) {
	ctx := context.Background()
	userEmail := "<EMAIL>"

	// Store original Redis client and restore after tests
	originalRDB := commonredis.RDB
	defer func() { commonredis.RDB = originalRDB }()

	t.Run("message in blacklisted folder - should skip", func(t *testing.T) {
		mockRDB, redisMock := redismock.NewClientMock()
		commonredis.RDB = mockRDB
		defer func() { _ = mockRDB.Close() }()

		client := &mock.Client{}
		redisMock.MatchExpectationsInOrder(false)

		// Set up cache expectations - return sentitems folder ID
		// Note: The mock returns folder.ID = folderName, so we expect "sentitems"
		sentitemsFolderID := "sentitems"
		redisMock.ExpectGet("<EMAIL>-sentitems").SetVal(`"` + sentitemsFolderID + `"`)

		// Set up expectations for other folders (cache miss)
		for _, folderName := range blackListedFolders[1:] {
			cacheKey := "<EMAIL>-" + folderName
			redisMock.ExpectGet(cacheKey).SetErr(redis.Nil)
		}

		// Set up expectations for caching uncached folders
		for _, folderName := range blackListedFolders[1:] {
			cacheKey := "<EMAIL>-" + folderName
			expectedValue, err := json.Marshal(folderName) // JSON-encode the folder ID
			require.NoError(t, err)
			redisMock.ExpectSet(cacheKey, expectedValue, folderCacheTTL).SetVal("OK")
		}

		msg := msclient.Message{
			ID:             "test-message-id",
			ParentFolderID: sentitemsFolderID, // Message is in sentitems folder
			IsDraft:        false,
		}

		webhookBody := msclient.Value{
			ChangeType: "created",
		}

		result := ShouldProcessMessage(ctx, userEmail, msg, webhookBody, client)

		assert.False(t, result)
		assert.NoError(t, redisMock.ExpectationsWereMet())
	})

	t.Run("message not in blacklisted folder - should process", func(t *testing.T) {
		mockRDB, redisMock := redismock.NewClientMock()
		commonredis.RDB = mockRDB
		defer func() { _ = mockRDB.Close() }()

		client := &mock.Client{}

		// Set up cache expectations for all blacklisted folders
		for _, folderName := range blackListedFolders {
			cacheKey := "<EMAIL>-" + folderName
			folderID := "folder-id-" + folderName
			redisMock.ExpectGet(cacheKey).SetVal(`"` + folderID + `"`)
		}

		msg := msclient.Message{
			ID:             "test-message-id",
			ParentFolderID: "inbox-folder-id", // Message is in inbox (not blacklisted)
			IsDraft:        false,
			Sender: msclient.Sender{
				EmailAddress: msclient.EmailAddress{Address: "<EMAIL>"},
			},
		}

		webhookBody := msclient.Value{
			ChangeType: "created",
		}

		result := ShouldProcessMessage(ctx, userEmail, msg, webhookBody, client)

		assert.True(t, result)
		assert.NoError(t, redisMock.ExpectationsWereMet())
	})

	t.Run("non-created change type - should skip", func(t *testing.T) {
		client := &mock.Client{}

		msg := msclient.Message{
			ID:             "test-message-id",
			ParentFolderID: "inbox-folder-id",
		}

		webhookBody := msclient.Value{
			ChangeType: "updated", // Not "created"
		}

		result := ShouldProcessMessage(ctx, userEmail, msg, webhookBody, client)

		assert.False(t, result)
		// No Redis calls should be made since we return early
		assert.Empty(t, client.Calls)
	})

	t.Run("draft message - should skip", func(t *testing.T) {
		mockRDB, redisMock := redismock.NewClientMock()
		commonredis.RDB = mockRDB
		defer func() { _ = mockRDB.Close() }()

		client := &mock.Client{}

		// Set up cache expectations
		for _, folderName := range blackListedFolders {
			cacheKey := "<EMAIL>-" + folderName
			folderID := "folder-id-" + folderName
			redisMock.ExpectGet(cacheKey).SetVal(`"` + folderID + `"`)
		}

		msg := msclient.Message{
			ID:             "test-message-id",
			ParentFolderID: "inbox-folder-id",
			IsDraft:        true, // This is a draft
		}

		webhookBody := msclient.Value{
			ChangeType: "created",
		}

		result := ShouldProcessMessage(ctx, userEmail, msg, webhookBody, client)

		assert.False(t, result)
		assert.NoError(t, redisMock.ExpectationsWereMet())
	})

	t.Run("user sent message to themselves - should process", func(t *testing.T) {
		mockRDB, redisMock := redismock.NewClientMock()
		commonredis.RDB = mockRDB
		defer func() { _ = mockRDB.Close() }()

		client := &mock.Client{}

		// Set up cache expectations
		for _, folderName := range blackListedFolders {
			cacheKey := "<EMAIL>-" + folderName
			folderID := "folder-id-" + folderName
			redisMock.ExpectGet(cacheKey).SetVal(`"` + folderID + `"`)
		}

		msg := msclient.Message{
			ID:             "test-message-id",
			ParentFolderID: "inbox-folder-id",
			IsDraft:        false,
			Sender: msclient.Sender{
				EmailAddress: msclient.EmailAddress{Address: userEmail},
			},
			ToRecipients: []msclient.RecipientCollection{
				{EmailAddress: msclient.EmailAddress{Address: userEmail}},
			},
		}

		webhookBody := msclient.Value{
			ChangeType: "created",
		}

		result := ShouldProcessMessage(ctx, userEmail, msg, webhookBody, client)

		assert.True(t, result)
		assert.NoError(t, redisMock.ExpectationsWereMet())
	})

	t.Run("user sent outgoing message - should skip", func(t *testing.T) {
		mockRDB, redisMock := redismock.NewClientMock()
		commonredis.RDB = mockRDB
		defer func() { _ = mockRDB.Close() }()

		client := &mock.Client{}

		// Set up cache expectations
		for _, folderName := range blackListedFolders {
			cacheKey := "<EMAIL>-" + folderName
			folderID := "folder-id-" + folderName
			redisMock.ExpectGet(cacheKey).SetVal(`"` + folderID + `"`)
		}

		msg := msclient.Message{
			ID:             "test-message-id",
			ParentFolderID: "inbox-folder-id",
			IsDraft:        false,
			Sender: msclient.Sender{
				EmailAddress: msclient.EmailAddress{Address: userEmail},
			},
			ToRecipients: []msclient.RecipientCollection{
				{EmailAddress: msclient.EmailAddress{Address: "<EMAIL>"}},
			},
		}

		webhookBody := msclient.Value{
			ChangeType: "created",
		}

		result := ShouldProcessMessage(ctx, userEmail, msg, webhookBody, client)

		assert.False(t, result)
		assert.NoError(t, redisMock.ExpectationsWereMet())
	})

	t.Run("folder lookup error - should process (fail open)", func(t *testing.T) {
		commonredis.RDB = nil // Simulate Redis unavailable

		// Create a client that will return an error for GetFolderByID
		client := &mock.Client{}
		// The mock client doesn't have a way to simulate errors,
		// but in this case Redis is nil so we fall back to API calls
		// and the mock will work normally

		msg := msclient.Message{
			ID:             "test-message-id",
			ParentFolderID: "inbox-folder-id",
			IsDraft:        false,
			Sender: msclient.Sender{
				EmailAddress: msclient.EmailAddress{Address: "<EMAIL>"},
			},
		}

		webhookBody := msclient.Value{
			ChangeType: "created",
		}

		result := ShouldProcessMessage(ctx, userEmail, msg, webhookBody, client)

		// Should process the message even though we couldn't get blacklisted folders
		// (This tests the fail-open behavior)
		assert.True(t, result)
	})
}

func TestGetBlacklistedFolderIDs(t *testing.T) {
	ctx := context.Background()
	userEmail := "<EMAIL>"

	// Store original Redis client and restore after tests
	originalRDB := commonredis.RDB
	defer func() { commonredis.RDB = originalRDB }()

	t.Run("redis unavailable - falls back to API", func(t *testing.T) {
		commonredis.RDB = nil

		client := &mock.Client{}
		client.Messages = make(map[string]msclient.Message)

		// The mock will return folder IDs based on the folder name
		folderMap, err := getBlacklistedFolderIDs(ctx, userEmail, client)

		require.NoError(t, err)
		assert.Len(t, folderMap, len(blackListedFolders))

		// Verify the mock was called for each blacklisted folder
		expectedCalls := len(blackListedFolders)
		assert.Len(t, client.Calls, expectedCalls)

		// Check that all expected folders are in the map
		for _, folderName := range blackListedFolders {
			assert.Equal(t, folderName, folderMap[folderName])
		}
	})

	t.Run("all folders cached in redis", func(t *testing.T) {
		mockRDB, redisMock := redismock.NewClientMock()
		commonredis.RDB = mockRDB
		defer func() { _ = mockRDB.Close() }()

		client := &mock.Client{}

		// Set up expectations for all blacklisted folders to be found in cache
		for _, folderName := range blackListedFolders {
			cacheKey := "<EMAIL>-" + folderName
			folderID := "folder-id-" + folderName

			redisMock.ExpectGet(cacheKey).SetVal(`"` + folderID + `"`)
		}

		folderMap, err := getBlacklistedFolderIDs(ctx, userEmail, client)

		require.NoError(t, err)
		assert.Len(t, folderMap, len(blackListedFolders))

		// Verify no API calls were made since everything was cached
		assert.Empty(t, client.Calls)

		// Verify all folders are in the map with correct names
		for _, folderName := range blackListedFolders {
			folderID := "folder-id-" + folderName
			assert.Equal(t, folderName, folderMap[folderID])
		}

		assert.NoError(t, redisMock.ExpectationsWereMet())
	})

	t.Run("partial cache hit - some folders cached, some not", func(t *testing.T) {
		mockRDB, redisMock := redismock.NewClientMock()
		commonredis.RDB = mockRDB
		defer func() { _ = mockRDB.Close() }()

		client := &mock.Client{}
		redisMock.MatchExpectationsInOrder(false)

		// Set up expectations: first 3 folders cached, rest not cached
		cachedFolders := blackListedFolders[:3]
		uncachedFolders := blackListedFolders[3:]

		// Cached folders
		for _, folderName := range cachedFolders {
			cacheKey := "<EMAIL>-" + folderName
			redisMock.ExpectGet(cacheKey).SetVal(`"` + folderName + `"`)
		}

		// Uncached folders - return cache miss
		for _, folderName := range uncachedFolders {
			cacheKey := "<EMAIL>-" + folderName
			redisMock.ExpectGet(cacheKey).SetErr(redis.Nil)
		}

		// Set up expectations for caching the uncached folders
		for _, folderName := range uncachedFolders {
			cacheKey := "<EMAIL>-" + folderName
			expectedValue, err := json.Marshal(folderName) // JSON-encode the folder ID
			require.NoError(t, err)
			redisMock.ExpectSet(cacheKey, expectedValue, folderCacheTTL).SetVal("OK")
		}

		folderMap, err := getBlacklistedFolderIDs(ctx, userEmail, client)

		require.NoError(t, err)
		assert.Len(t, folderMap, len(blackListedFolders))

		// Verify API calls were made only for uncached folders
		assert.Len(t, client.Calls, len(uncachedFolders))

		// Verify all folders are in the map
		for _, folderName := range blackListedFolders {
			assert.Equal(t, folderName, folderMap[folderName])
		}

		assert.NoError(t, redisMock.ExpectationsWereMet())
	})

	t.Run("redis get error - treats as cache miss", func(t *testing.T) {
		mockRDB, redisMock := redismock.NewClientMock()
		commonredis.RDB = mockRDB
		defer func() { _ = mockRDB.Close() }()

		client := &mock.Client{}
		redisMock.MatchExpectationsInOrder(false)

		// Set up expectations for Redis errors on all folders
		for _, folderName := range blackListedFolders {
			cacheKey := "<EMAIL>-" + folderName
			redisMock.ExpectGet(cacheKey).SetErr(errors.New("redis connection error"))
		}

		// Set up expectations for caching all folders after API calls
		// Note: The mock returns folder.ID = folderName
		for _, folderName := range blackListedFolders {
			cacheKey := "<EMAIL>-" + folderName
			expectedValue, err := json.Marshal(folderName) // JSON-encode the folder ID
			require.NoError(t, err)
			redisMock.ExpectSet(cacheKey, expectedValue, folderCacheTTL).SetVal("OK")
		}

		folderMap, err := getBlacklistedFolderIDs(ctx, userEmail, client)

		require.NoError(t, err)
		assert.Len(t, folderMap, len(blackListedFolders))

		// Verify API calls were made for all folders
		assert.Len(t, client.Calls, len(blackListedFolders))

		// Verify all folders are in the map
		for _, folderName := range blackListedFolders {
			assert.Equal(t, folderName, folderMap[folderName])
		}

		assert.NoError(t, redisMock.ExpectationsWereMet())
	})

	t.Run("redis set error - continues operation", func(t *testing.T) {
		mockRDB, redisMock := redismock.NewClientMock()
		commonredis.RDB = mockRDB
		defer func() { _ = mockRDB.Close() }()

		client := &mock.Client{}

		// Set up expectations for cache misses and set errors
		// Use MatchExpectationsInOrder(false) to allow any order
		redisMock.MatchExpectationsInOrder(false)

		for _, folderName := range blackListedFolders {
			cacheKey := "<EMAIL>-" + folderName
			// Expect cache miss
			redisMock.ExpectGet(cacheKey).SetErr(redis.Nil)

			// Expect set operation to fail
			// Note: redis.SetKey JSON-marshals the data
			expectedValue, err := json.Marshal(folderName) // JSON-encode the folder ID
			require.NoError(t, err)
			redisMock.ExpectSet(cacheKey, expectedValue, folderCacheTTL).SetErr(errors.New("redis set error"))
		}

		folderMap, err := getBlacklistedFolderIDs(ctx, userEmail, client)

		require.NoError(t, err)
		assert.Len(t, folderMap, len(blackListedFolders))

		// Operation should continue despite cache errors
		assert.Len(t, client.Calls, len(blackListedFolders))

		// Verify the folder map contains the expected mappings
		// The mock returns folder.ID = folderName, so the map should be folderName -> folderName
		// The real MSGraph API does return an alphanumeric ID for the well-known folder name
		for _, folderName := range blackListedFolders {
			assert.Equal(t, folderName, folderMap[folderName])
		}

		assert.NoError(t, redisMock.ExpectationsWereMet())
	})
}

func TestGetBlacklistedFolderIDsFromAPI(t *testing.T) {
	ctx := context.Background()
	client := &mock.Client{}

	t.Run("successful API calls for all folders", func(t *testing.T) {
		folderMap, err := getBlacklistedFolderIDsFromAPI(ctx, client)

		require.NoError(t, err)
		assert.Len(t, folderMap, len(blackListedFolders))
		assert.Len(t, client.Calls, len(blackListedFolders))

		// Verify all expected folders are present
		for _, folderName := range blackListedFolders {
			assert.Equal(t, folderName, folderMap[folderName])
		}
	})

	t.Run("API errors are handled gracefully", func(t *testing.T) {
		// This test would require a more sophisticated mock that can simulate API errors
		// For now, the mock always succeeds, but in a real scenario some API calls might fail
		folderMap, err := getBlacklistedFolderIDsFromAPI(ctx, client)

		require.NoError(t, err)
		// Even if some API calls fail, we should still get results for successful ones
		assert.GreaterOrEqual(t, len(folderMap), 0)
	})
}
