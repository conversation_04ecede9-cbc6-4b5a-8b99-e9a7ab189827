package graceful

import (
	"context"
	"os"
	"os/signal"
	"strconv"
	"sync"
	"syscall"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/redis"
)

// ShutdownHandler manages graceful shutdown of services
type ShutdownHandler struct {
	cleanupFuncs []func(context.Context) error
	mu           sync.RWMutex
	shutdownOnce sync.Once
	timeout      time.Duration
}

// NewShutdownHandler creates a new shutdown handler with the specified timeout
func NewShutdownHandler(timeout time.Duration) *ShutdownHandler {
	return &ShutdownHandler{
		timeout: timeout,
	}
}

// NewLambdaShutdownHandler creates a new shutdown handler with configurable timeout
func NewLambdaShutdownHandler(timeout time.Duration) *ShutdownHandler {
	if timeout == 0 {
		timeout = 15 * time.Second // Default to 15 seconds for Lambda
	}
	return NewShutdownHandler(timeout)
}

// NewLambdaShutdownHandlerDefault creates a new shutdown handler with default timeout
// Uses 15 seconds timeout - suitable for most Lambda functions
func NewLambdaShutdownHandlerDefault() *ShutdownHandler {
	return NewLambdaShutdownHandler(15 * time.Second)
}

// AddCleanupFunc adds a cleanup function to be executed during shutdown
func (h *ShutdownHandler) AddCleanupFunc(fn func(context.Context) error) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.cleanupFuncs = append(h.cleanupFuncs, fn)
}

// Shutdown executes all cleanup functions with the specified timeout
func (h *ShutdownHandler) Shutdown(ctx context.Context) error {
	var shutdownErr error
	h.shutdownOnce.Do(func() {
		h.mu.RLock()
		cleanupFuncs := make([]func(context.Context) error, len(h.cleanupFuncs))
		copy(cleanupFuncs, h.cleanupFuncs)
		h.mu.RUnlock()

		if len(cleanupFuncs) == 0 {
			log.Debug(ctx, "no cleanup functions registered")
			return
		}

		log.Info(
			ctx,
			"starting graceful shutdown",
			zap.Int("cleanupFunctions", len(cleanupFuncs)),
			zap.Duration("timeout", h.timeout),
		)

		// Create timeout context for cleanup
		cleanupCtx, cancel := context.WithTimeout(ctx, h.timeout)
		defer cancel()

		// Execute cleanup functions concurrently
		var wg sync.WaitGroup
		errChan := make(chan error, len(cleanupFuncs))

		for i, cleanupFunc := range cleanupFuncs {
			wg.Add(1)
			go func(fn func(context.Context) error, index int) {
				defer wg.Done()

				start := time.Now()
				if err := fn(cleanupCtx); err != nil {
					log.Error(
						cleanupCtx,
						"cleanup function failed",
						zap.Error(err),
						zap.Int("functionIndex", index),
					)
					errChan <- err
				} else {
					log.Debug(
						cleanupCtx,
						"cleanup function completed successfully",
						zap.Int("functionIndex", index),
						zap.Duration("duration", time.Since(start)),
					)
				}
			}(cleanupFunc, i)
		}

		// Wait for all cleanup functions to complete or timeout
		done := make(chan struct{})
		go func() {
			wg.Wait()
			close(done)
		}()

		select {
		case <-done:
			log.Info(ctx, "all cleanup functions completed")
		case <-cleanupCtx.Done():
			log.Warn(ctx, "cleanup timeout exceeded, some functions may not have completed")
		}

		// Collect any errors
		close(errChan)
		var errs []error
		for err := range errChan {
			errs = append(errs, err)
		}

		if len(errs) > 0 {
			log.Error(
				ctx,
				"some cleanup functions failed",
				zap.Int("errorCount", len(errs)),
				zap.Errors("errors", errs),
			)
			shutdownErr = errs[0] // Store first error
		}

		log.Info(ctx, "graceful shutdown completed successfully")
	})

	return shutdownErr
}

// HandleOSSignals sets up signal handling for graceful shutdown and returns a channel
// that will receive an error when a signal is caught and shutdown is complete.
// The main application goroutine should block on this channel.
func (h *ShutdownHandler) HandleOSSignals(ctx context.Context) <-chan error {
	sigChan := make(chan os.Signal, 1)
	errChan := make(chan error, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func(parentCtx context.Context) {
		sig := <-sigChan
		log.Info(parentCtx, "received shutdown signal, starting cleanup", zap.String("signal", sig.String()))

		// Create a new context with a timeout for the shutdown process,
		// derived from the parent context.
		shutdownCtx, cancel := context.WithTimeout(parentCtx, h.timeout)
		defer cancel()

		if err := h.Shutdown(shutdownCtx); err != nil {
			log.Error(shutdownCtx, "error during graceful shutdown", zap.Error(err))
			errChan <- err
		} else {
			log.Info(shutdownCtx, "graceful shutdown completed")
			errChan <- nil
		}
	}(ctx)

	return errChan
}

// GetTimeout returns the configured timeout
func (h *ShutdownHandler) GetTimeout() time.Duration {
	return h.timeout
}

// TestShutdown manually triggers shutdown for testing purposes
func (h *ShutdownHandler) TestShutdown() error {
	log.Info(context.Background(), "testing shutdown handler manually")
	return h.Shutdown(context.Background())
}

// SetupGracefulShutdown is a reusable function that sets up graceful shutdown
// for Lambda functions with standard cleanup functions for RDS, Redis, and logging.
// This function can be used across all Lambda functions to ensure consistent shutdown handling.
func SetupGracefulShutdown(ctx context.Context, appName string) (*ShutdownHandler, <-chan error) {
	// Get shutdown timeout from environment or use default
	shutdownTimeout := 15 * time.Second
	if timeoutStr := os.Getenv("SHUTDOWN_TIMEOUT_SECONDS"); timeoutStr != "" {
		if timeout, err := strconv.Atoi(timeoutStr); err == nil && timeout > 0 {
			shutdownTimeout = time.Duration(timeout) * time.Second
		}
	}

	// Initialize graceful shutdown handler with configurable timeout
	shutdownHandler := NewLambdaShutdownHandler(shutdownTimeout)
	log.Info(ctx, "graceful shutdown handler initialized",
		zap.Duration("timeout", shutdownTimeout),
		zap.String("app", appName))

	// Also set up OS signal handling for local development (Ctrl+C) and
	// standard Lambda container termination (SIGTERM). This is the primary
	// mechanism for graceful shutdown in a typical Lambda environment.
	shutdownCompleteChan := shutdownHandler.HandleOSSignals(ctx)

	// Add standard cleanup functions that most Lambdas need
	shutdownHandler.AddCleanupFunc(func(ctx context.Context) error {
		log.Info(ctx, "closing RDS connections")
		return rds.Close(ctx)
	})

	shutdownHandler.AddCleanupFunc(func(ctx context.Context) error {
		log.Info(ctx, "closing Redis connections")
		return redis.Close(ctx)
	})

	shutdownHandler.AddCleanupFunc(func(ctx context.Context) error {
		log.Info(ctx, "flushing logs")
		log.Flush(ctx)
		return nil
	})

	return shutdownHandler, shutdownCompleteChan
}
