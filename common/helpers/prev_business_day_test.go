package helpers

import (
	"testing"
	"time"
)

func TestGetPreviousBusinessDay(t *testing.T) {
	tests := []struct {
		name     string
		input    time.Time
		expected time.Time
	}{
		{
			name:     "Tuesday to Monday",
			input:    time.Date(2024, 1, 16, 12, 0, 0, 0, time.UTC), // Tuesday
			expected: time.Date(2024, 1, 15, 12, 0, 0, 0, time.UTC), // Monday
		},
		{
			name:     "Wednesday to Tuesday",
			input:    time.Date(2024, 1, 17, 12, 0, 0, 0, time.UTC), // Wednesday
			expected: time.Date(2024, 1, 16, 12, 0, 0, 0, time.UTC), // Tuesday
		},
		{
			name:     "Thursday to Wednesday",
			input:    time.Date(2024, 1, 18, 12, 0, 0, 0, time.UTC), // Thursday
			expected: time.Date(2024, 1, 17, 12, 0, 0, 0, time.UTC), // Wednesday
		},
		{
			name:     "Friday to Thursday",
			input:    time.Date(2024, 1, 19, 12, 0, 0, 0, time.UTC), // Friday
			expected: time.Date(2024, 1, 18, 12, 0, 0, 0, time.UTC), // Thursday
		},
		{
			name:     "Monday to Friday (previous week)",
			input:    time.Date(2024, 1, 15, 12, 0, 0, 0, time.UTC), // Monday
			expected: time.Date(2024, 1, 12, 12, 0, 0, 0, time.UTC), // Friday (previous week)
		},
		{
			name:     "Saturday to Friday",
			input:    time.Date(2024, 1, 20, 12, 0, 0, 0, time.UTC), // Saturday
			expected: time.Date(2024, 1, 19, 12, 0, 0, 0, time.UTC), // Friday
		},
		{
			name:     "Sunday to Friday",
			input:    time.Date(2024, 1, 21, 12, 0, 0, 0, time.UTC), // Sunday
			expected: time.Date(2024, 1, 19, 12, 0, 0, 0, time.UTC), // Friday
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetPreviousBusinessDay(tt.input)
			if !result.Equal(tt.expected) {
				t.Errorf("GetPreviousBusinessDay(%v) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestGetPreviousBusinessDay_WeekdayValidation(t *testing.T) {
	// Test that the result is always a weekday
	testDates := []time.Time{
		time.Date(2024, 1, 15, 12, 0, 0, 0, time.UTC), // Monday
		time.Date(2024, 1, 16, 12, 0, 0, 0, time.UTC), // Tuesday
		time.Date(2024, 1, 17, 12, 0, 0, 0, time.UTC), // Wednesday
		time.Date(2024, 1, 18, 12, 0, 0, 0, time.UTC), // Thursday
		time.Date(2024, 1, 19, 12, 0, 0, 0, time.UTC), // Friday
		time.Date(2024, 1, 20, 12, 0, 0, 0, time.UTC), // Saturday
		time.Date(2024, 1, 21, 12, 0, 0, 0, time.UTC), // Sunday
	}

	for _, input := range testDates {
		result := GetPreviousBusinessDay(input)
		weekday := result.Weekday()

		if weekday == time.Saturday || weekday == time.Sunday {
			t.Errorf("GetPreviousBusinessDay(%v) returned %v (weekend), expected weekday", input, weekday)
		}
	}
}
