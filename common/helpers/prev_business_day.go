package helpers

import "time"

func GetPreviousBusinessDay(t time.Time) time.Time {
	// Get previous day
	prevDay := t.AddDate(0, 0, -1)

	// If the previous day is a weekend, recurse to find the previous business day
	if prevDay.Weekday() == time.Saturday || prevDay.Weekday() == time.Sunday {
		return GetPreviousBusinessDay(prevDay)
	}

	// Otherwise, return the previous day (which is a business day)
	return prevDay
}
