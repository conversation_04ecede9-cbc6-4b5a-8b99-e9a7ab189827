package tmslocation

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// See tms_location/query_test.go for query performance tests

// Tests creating TMSLocation in DB and soft deleting it
func TestLiveTMSLocationRDS(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping live DB test: set TEST_LIVE_DB=true to enable")
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	tms := rds.CreateTestIntegrationByType(ctx, t, service.ID, models.TMS)
	tmsCarrier := rds.CreateTestCarrier(ctx, t, service.ID, tms.ID)

	location := models.TMSLocation{
		TMSIntegrationID: tms.ID,
		FullAddress:      "123 Main St, Test City, TS, 12345",
		Latitude:         40.0,
		Longitude:        -75.0,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: "1234567890",
			Name:          "Test Location",
		},
		TMSCarrierID: tmsCarrier.ID,
	}
	err := Create(ctx, location)
	require.NoError(t, err)

	// Fetch and assert location exists
	var fetchedLocation models.TMSLocation
	err = rds.WithContext(ctx).Where("external_tms_id = ?", "1234567890").First(&fetchedLocation).Error
	require.NoError(t, err)
	assert.Equal(t, "Test Location", fetchedLocation.Name)

	// Soft delete the location
	err = DeleteLocation(ctx, fetchedLocation.ID)
	require.NoError(t, err)

	// Assert soft delete (deleted_at is set)
	var deleted models.TMSLocation
	err = rds.WithContext(ctx).Unscoped().First(&deleted, fetchedLocation.ID).Error
	require.NoError(t, err)
	assert.NotNil(t, deleted.DeletedAt)
	assert.WithinDuration(t, time.Now(), deleted.DeletedAt.Time, time.Minute)
}
