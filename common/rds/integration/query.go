package integration

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type Column string

const (
	LastCustomerUpdatedAt Column = "lastCustomerUpdatedAt"
	LastLocationUpdatedAt Column = "lastLocationUpdatedAt"
)

var (
	// McLeod Enterprise regex patterns
	TridentMcleodLoadIDPattern  = regexp.MustCompile(`\b\d{7}\b`)
	FetchFreightMcleodIDPattern = regexp.MustCompile(`\b00(3|4|5)\d{4}\b`)
	McleodMovementIDPattern     = regexp.MustCompile(`\b\d{6}\b`)
	McleodRefNumberPattern      = regexp.MustCompile(`\b[a-zA-Z0-9\-_#]{5,20}\b`)

	// BIT
	// Define pattern to TMS name mappings
	patternMappings = []struct {
		pattern *regexp.Regexp
		name    models.IntegrationName
	}{
		// NOTE: Add new patterns here and in ../../common/emails/tracker.go
		{regexp.MustCompile(`\b2\d{6}\b`), models.Aljex},         // NFI
		{regexp.MustCompile(`\b1\d{5}\b`), models.Aljex},         // Able
		{regexp.MustCompile(`\b(\d{5}|1\d{5})\b`), models.Aljex}, // MLM
		// Relay Load ID; staging starts w/ 2, prod w/ 3
		{regexp.MustCompile(`\b(2|3)\d{6}\b`), models.Relay},
		// Relay Booking ID; staging prefix 7, prod is 8
		{regexp.MustCompile(`\b(7|8)\d{6}\b`), models.Relay},
		// Turvo Custom ID
		{regexp.MustCompile(`\b([a-zA-Z\d]{5,}-\d{5})\b`), models.Turvo},
		// Turvo SC Number ID
		{regexp.MustCompile(`\bSC\d+\b`), models.Turvo},
		{regexp.MustCompile(`\b3\d{7}\b`), models.GlobalTranzTMS},     // GlobalTranz Load ID
		{regexp.MustCompile(`\b\d{4}\b`), models.Ascend},              // Ascend Load ID
		{regexp.MustCompile(`\b\d{9}\b`), models.Tai},                 // Tai Load ID
		{regexp.MustCompile(`\b\d{5,10}\b`), models.Turvo},            // Tai PO #s, Aljex Customer Refs
		{TridentMcleodLoadIDPattern, models.McleodEnterprise},         // Trident Mcleod Load ID
		{FetchFreightMcleodIDPattern, models.McleodEnterprise},        // Fetch Freight Mcleod ID
		{McleodMovementIDPattern, models.McleodEnterprise},            // Mcleod Movement ID
		{regexp.MustCompile(`\b\d{8}[A-Z]{3}\b`), models.MercuryGate}, // Pattern to match IDs like 10022096EVL
		{regexp.MustCompile(`\b[A-Z]{2,4}\d{6,8}\b`), models.ThreeG},  // Pattern to match IDs like **********
		// Pattern to match IDs like 15970-DN or 15970-FAK
		{regexp.MustCompile(`\b\d{5}-[A-Z]{2,3}\b`), models.FreightFlow},
		{McleodRefNumberPattern, models.McleodEnterprise},
		// We allow the ref number to be any alpha and/or numeric string because Go regex doesn't support look-aheads.
		// Unlike in Processor, we don't verify the candidate ID has at least one number because this is used when the
		// user makes a direct search and thus unlikely to overload system with plain words like "hello"
	}
)

func Get(ctx context.Context, id uint) (integration models.Integration, err error) {
	return integration, rds.WithContextReader(ctx).
		Where("id = ?", id).
		Where("deleted_at IS NULL AND disabled = FALSE").
		First(&integration).Error
}

func GetByServiceID(ctx context.Context, serviceID uint) (integration models.Integration, err error) {
	return integration, rds.WithContextReader(ctx).
		Where("service_id = ?", serviceID).
		Where("deleted_at IS NULL AND disabled = FALSE").
		First(&integration).Error
}

// Be careful with this function because it will return:
//
// 1. An integration even if it's disabled
//
// 2. The first integration of a certain type even if the service has multiple integrations of that type.
//
// More often than not you should get list of integrations and filter out accordingly.
func GetByServiceIDAndType(
	ctx context.Context,
	serviceID uint,
	integrationType models.IntegrationType,
) (integration models.Integration, err error) {

	err = rds.WithContextReader(ctx).
		Where("service_id = ? AND type = ? AND disabled = FALSE", serviceID, integrationType).
		First(&integration).
		Error

	return integration, err
}

func GetTMSListByServiceID(ctx context.Context, serviceID uint) (integrations []models.Integration, err error) {
	err = rds.WithContextReader(ctx).
		Where("type = 'tms'").
		Where("service_id = ?", serviceID).
		Where("deleted_at IS NULL AND disabled = FALSE").
		Find(&integrations).Error

	return integrations, err
}

func GetCarrierVerificationByServiceID(
	ctx context.Context,
	serviceID uint,
) (integration models.Integration, err error) {

	err = rds.WithContextReader(ctx).
		Where("type LIKE ?", "%carrierverification%").
		Where("service_id = ?", serviceID).
		Where("deleted_at IS NULL AND disabled = FALSE").
		Table("integrations").
		First(&integration).Error

	return integration, err
}

func GetPricingByServiceID(ctx context.Context, serviceID uint) (integrations []models.Integration, err error) {
	err = rds.WithContextReader(ctx).
		Where("type LIKE ?", "%pricing%").
		Where("service_id = ?", serviceID).
		Where("disabled = FALSE AND deleted_at IS NULL").
		Table("integrations").
		Scan(&integrations).Debug().Error

	return integrations, err
}

func GetInternalGreenscreensStaging(ctx context.Context) (integration models.Integration, err error) {
	err = rds.WithContextReader(ctx).
		Where("service_id = 34"). // Outlook internal service
		Where("type = 'pricing' AND name = 'greenscreens'").
		Where("disabled = FALSE AND deleted_at IS NULL").
		First(&integration).Error

	return integration, err
}

func GetInternalSchedulerByName(
	ctx context.Context,
	integrationName models.IntegrationName,
) (integration models.Integration, err error) {

	err = rds.WithContextReader(ctx).
		Where("service_id = 1 OR service_id = 101"). // Gmail and Outlook internal services
		Where("type = 'scheduling'").
		Where("name = ?", integrationName).
		Where("disabled = FALSE AND deleted_at IS NULL").
		First(&integration).Error

	return integration, err
}

// Special case for Opendock poller where we <NAME_EMAIL>
func GetInternalOpendockForPoller(
	ctx context.Context,
) (integration models.Integration, err error) {
	return integration, rds.WithContextReader(ctx).
		Where("service_id = 1 AND username = '<EMAIL>'").
		Where("type = 'scheduling' AND name = 'opendock' AND disabled = FALSE").
		First(&integration).Error
}

func GetAllSchedulerByServiceIDAndUserID(
	ctx context.Context,
	userID,
	serviceID uint,
) ([]models.Integration, error) {

	// Find all group-specific scheduling integrations for this user
	var userGroupSchedulers []models.Integration
	userGroupErr := rds.WithContextReader(ctx).Raw(
		`SELECT * FROM integrations WHERE id IN (
			SELECT integration_id
			FROM user_group_integrations
			WHERE user_group_id IN (
				SELECT user_group_id
				FROM user_group_users
				WHERE user_id = ?
			)
		)
		AND type = 'scheduling'
		AND is_service_wide = FALSE
		AND service_id = ?
		AND disabled = FALSE
		AND deleted_at IS NULL`,
		userID,
		serviceID,
	).Find(&userGroupSchedulers).Error

	// Find all service-wide scheduling integrations
	var serviceWideSchedulers []models.Integration
	serviceWideErr := rds.WithContextReader(ctx).
		Where("type = 'scheduling'").
		Where("service_id = ?", serviceID).
		Where("is_service_wide = TRUE").
		Where("disabled = ?", false).
		Where("deleted_at IS NULL").
		Find(&serviceWideSchedulers).Error

	integrations := make([]models.Integration, 0, len(userGroupSchedulers)+len(serviceWideSchedulers))
	integrations = append(integrations, userGroupSchedulers...)
	integrations = append(integrations, serviceWideSchedulers...)

	return integrations, errors.Join(userGroupErr, serviceWideErr)
}

// GetSchedulerByServiceUserIDsAndName returns a scheduler integration for a user. First checks for group-specific
// integrations (is_service_wide = FALSE) as they take precedence over service-wide ones. This allows for
// customer-specific configurations, primarily used by Opendock accounts for NFI where different user groups need
// different integration settings. If no group-specific integration is found, falls back to service-wide integration.
//
// NOTE: This paradigm assumes that there is only ONE instance of an integration per-group and per-service, e.g.
// a group does not have multiple Retalix integrations, and a service does not have have multiple service-wide Retalix.
// But a group/service can have 1 OpenDock, 1 Retalix, etc.
func GetSchedulerByServiceUserIDsAndName(
	ctx context.Context,
	userID uint,
	serviceID uint,
	integrationName string,
	integrationID uint,
) (integration models.Integration, err error) {

	// First, try to find a group-specific integration for this user
	var userGroupSchedulers []models.Integration
	err = rds.WithContextReader(ctx).Raw(
		`SELECT *
		FROM integrations
		WHERE id IN (
			SELECT integration_id
			FROM user_group_integrations
			WHERE user_group_id IN (
				SELECT user_group_id
				FROM user_group_users
				WHERE user_id = ?
			)
		)
		AND type = 'scheduling'
		AND service_id = ?
		AND name = ?
		AND disabled = FALSE
		AND deleted_at IS NULL
		AND is_service_wide = FALSE
    `, userID, serviceID, integrationName).
		Find(&userGroupSchedulers).Error

	if len(userGroupSchedulers) > 0 && userGroupSchedulers[0].ID != 0 {
		if len(userGroupSchedulers) > 1 {
			log.Warn(
				ctx,
				"unexpected: multiple scheduler integrations found for group of the same name, defaulting to first",
				zap.Uint("serviceID", serviceID),
				zap.String("integrationName", integrationName),
				zap.Any("integrations", userGroupSchedulers),
			)
		}

		if integrationID != 0 {
			for _, integration := range userGroupSchedulers {
				if integration.ID == integrationID {
					log.Info(ctx, "found matching integrationID", zap.Uint("integrationID", integrationID))
					return integration, err
				}
			}
		}

		log.Info(ctx, "no matching integrationID, returning first group-specific integration")
		return userGroupSchedulers[0], err
	}

	// If no group-specific integration found, fall back to service-wide integration
	// These are integrations available to all users in the service
	var serviceWideSchedulers []models.Integration
	err = rds.WithContextReader(ctx).
		Where("type = 'scheduling'").
		Where("service_id = ?", serviceID).
		Where("name = ?", integrationName).
		Where("is_service_wide = TRUE").
		Where("disabled = FALSE AND deleted_at IS NULL").
		Find(&serviceWideSchedulers).Error

	if len(serviceWideSchedulers) == 0 {
		return models.Integration{}, gorm.ErrRecordNotFound
	}

	if len(serviceWideSchedulers) > 1 {
		log.Warn(
			ctx,
			"unexpected: multiple scheduler integrations found for service-wide of the same name, defaulting to first",
			zap.Uint("serviceID", serviceID),
			zap.String("integrationName", integrationName),
			zap.Any("integrations", serviceWideSchedulers),
		)
	}

	if integrationID != 0 {
		for _, integration := range serviceWideSchedulers {
			if integration.ID == integrationID {
				log.Info(ctx, "found matching integrationID", zap.Uint("integrationID", integrationID))
				return integration, err
			}
		}
	}

	log.Info(ctx, "no matching integrationID, returning first service-wide integration")
	return serviceWideSchedulers[0], err
}

func GetGroupIntegrationsByUser(
	ctx context.Context,
	user models.User,
) (integrations []models.Integration, err error) {

	err = rds.WithContextReader(ctx).Raw(
		`SELECT *
		FROM integrations
		WHERE id IN (
			SELECT integration_id FROM user_group_integrations WHERE user_group_id IN (
				SELECT user_group_id FROM user_group_users WHERE user_id = ?
			)
			AND integrations.service_id = ?
			AND deleted_at IS NULL
		)`, user.ID, user.ServiceID).
		Find(&integrations).Error

	return integrations, err
}

func GetGroupIntegrationsByUserAndServiceID(
	ctx context.Context,
	user models.User,
) (integrations []models.Integration, err error) {

	err = rds.WithContextReader(ctx).Raw(
		`SELECT DISTINCT i.*
			FROM integrations i
			WHERE i.service_id = ?
			AND i.deleted_at IS NULL
			AND (
				i.is_service_wide = true
				OR EXISTS (
					SELECT 1
					FROM user_group_integrations ugi
					JOIN user_group_users ugu ON ugi.user_group_id = ugu.user_group_id
					WHERE ugi.integration_id = i.id
					AND ugu.user_id = ?
			)
		)`, user.ServiceID, user.ID).
		Find(&integrations).Error

	return integrations, err
}

func GetCompanyIntegrationsByServiceID(
	ctx context.Context,
	serviceID uint,
	user models.User,
) (integrations []models.Integration, err error) {

	query := `
		SELECT DISTINCT i.*
		FROM integrations i
		WHERE i.service_id = ?
		  AND i.deleted_at IS NULL

		EXCEPT

		SELECT DISTINCT i.*
		FROM integrations i
		WHERE i.service_id = ?
		  AND i.deleted_at IS NULL
		  AND (
		    i.is_service_wide = true
		    OR EXISTS (
		      SELECT 1
		      FROM user_group_integrations ugi
		      JOIN user_group_users ugu 
		        ON ugi.user_group_id = ugu.user_group_id
		      WHERE ugi.integration_id = i.id
		        AND ugu.user_id = ?
		    )
		  )
	`

	err = rds.WithContextReader(ctx).Raw(query, serviceID, serviceID, user.ID).
		Find(&integrations).Error

	return integrations, err
}

func AssociateWithUserGroup(ctx context.Context, integrationID, userGroupID uint) error {
	db := rds.WithContext(ctx)

	// Check if association already exists
	var count int64
	err := db.Raw(
		`SELECT COUNT(*) FROM user_group_integrations 
		 WHERE user_group_id = ? AND integration_id = ?`,
		userGroupID, integrationID).Scan(&count).Error

	if err != nil {
		return fmt.Errorf("failed to check existing association: %w", err)
	}

	if count > 0 {
		// Association already exists, no need to create
		return nil
	}

	// Create the association
	err = db.Exec(
		`INSERT INTO user_group_integrations (user_group_id, integration_id) 
		 VALUES (?, ?)`,
		userGroupID, integrationID).Error

	if err != nil {
		return fmt.Errorf("failed to create user group integration association: %w", err)
	}

	return nil
}

func GetByName(
	ctx context.Context,
	serviceID uint,
	name models.IntegrationName,
) (integration models.Integration, err error) {

	return integration, rds.WithContextReader(ctx).
		Preload("Service").
		Where("service_id = ? AND name = ?", serviceID, name).
		Where("disabled = FALSE AND deleted_at IS NULL").
		First(&integration).Error
}

// GetColumn retrieves the specified timestamp column from an integration
func GetColumn(ctx context.Context, tmsID uint, name Column) (time.Time, error) {
	var integration models.Integration
	if err := rds.WithContextReader(ctx).Where("id = ?", tmsID).First(&integration).Error; err != nil {
		return time.Time{}, err
	}

	var timestamp time.Time
	switch name {
	case LastCustomerUpdatedAt:
		timestamp = integration.LastCustomerUpdatedAt
	case LastLocationUpdatedAt:
		timestamp = integration.LastLocationUpdatedAt
	default:
		return time.Time{}, fmt.Errorf("invalid column name: %s", name)
	}

	if timestamp.IsZero() {
		return time.Time{}, fmt.Errorf("column %s is not set for integration %d", name, tmsID)
	}

	return timestamp, nil
}

func SetColumn(ctx context.Context, tmsID uint, name Column, value time.Time) error {
	var gormColumnName string
	switch name {
	case LastCustomerUpdatedAt:
		gormColumnName = "last_customer_updated_at"
	case LastLocationUpdatedAt:
		gormColumnName = "last_location_updated_at"
	default:
		return fmt.Errorf("invalid column name: %s", name)
	}

	return rds.WithContext(ctx).
		Model(&models.Integration{}).
		Where("id = ?", tmsID).
		Update(gormColumnName, value).Error
}

func GetFirstValidGlobalTranz(ctx context.Context) (globaltranzIntegration models.Integration, err error) {
	return globaltranzIntegration, rds.WithContextReader(ctx).
		Where("type = 'pricing' AND name = 'globaltranz' AND disabled = FALSE AND deleted_at IS NULL").
		First(&globaltranzIntegration).Error
}

func GetAllValidTMS(ctx context.Context) (tmsIntegrations []models.Integration, err error) {
	return tmsIntegrations, rds.WithContextReader(ctx).
		Where("type = 'tms' AND disabled = FALSE AND deleted_at IS NULL").
		Find(&tmsIntegrations).Error
}

func GetAllValidTurvo(ctx context.Context) (turvoIntegrations []models.Integration, err error) {
	return turvoIntegrations, rds.WithContextReader(ctx).
		Where("name = 'turvo'").
		Where("disabled = FALSE AND deleted_at IS NULL").
		Find(&turvoIntegrations).Error
}

func GetAllValidAscend(ctx context.Context) (ascendIntegrations []models.Integration, err error) {
	return ascendIntegrations, rds.WithContextReader(ctx).
		Where("name = 'ascend'").
		Where("disabled = FALSE AND deleted_at IS NULL").
		Find(&ascendIntegrations).Error
}

/*
This is somewhat hardcoded as of now since Turvo & Tai are the only TMS that supports posting Quotes.

If we ever have multiple TMSes that can submit Quote for a service:
We can have a customer-regex matching rule like the one for FreightIDs when working with loads,
having a 'primary_quote_tms' column or even allowing the user to select the TMS on the FE.
*/
func GetTMSForQuoteByServiceID(ctx context.Context, serviceID uint) (integration models.Integration, err error) {
	return integration, rds.WithContextReader(ctx).
		Where("service_id = ? AND (name = 'turvo' OR name = 'tai') AND disabled = FALSE", serviceID).
		Find(&integration).
		Error
}

func MatchTMSByServiceAndFreightID(
	ctx context.Context,
	serviceID uint,
	freightID string,
) (tmsIntegration *models.Integration, err error) {

	tmsList, err := GetTMSListByServiceID(ctx, serviceID)
	if err != nil {
		return nil, err
	}

	if len(tmsList) == 0 {
		return nil, fmt.Errorf("error getting TMS list: %w", gorm.ErrRecordNotFound)
	}

	if len(tmsList) == 1 {
		return &tmsList[0], nil
	}

	// Check each pattern and find matching TMS
	for _, mapping := range patternMappings {
		if mapping.pattern.MatchString(freightID) {
			// Look for this TMS in the service's TMS list
			for _, tms := range tmsList {
				if tms.Name == mapping.name {
					return &tms, nil
				}
			}
		}
	}

	return nil, fmt.Errorf("could not match integration with freight id: %s", freightID)
}
