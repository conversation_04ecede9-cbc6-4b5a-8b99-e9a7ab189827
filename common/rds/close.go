package rds

import (
	"context"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// Close gracefully closes all database connections
func Close(ctx context.Context) error {
	var errs []error

	// Close main database connection
	if db != nil {
		if sqlDB, err := db.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				// Check if it's the benign TLS closeNotify error
				if isTLSCloseNotifyError(err) {
					log.WarnNoSentry(
						ctx,
						"database connection closed with TLS closeNotify warning (connection already closed)",
						zap.Error(err),
					)
				} else {
					log.Error(ctx, "failed to close main database connection", zap.Error(err))
					errs = append(errs, err)
				}
			} else {
				log.Info(ctx, "main database connection closed successfully")
			}
		}
		db = nil
	}

	// Close reader database connection
	if dbReader != nil {
		if sqlDB, err := dbReader.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				// Check if it's the benign TLS closeNotify error
				if isTLSCloseNotifyError(err) {
					log.WarnNoSentry(
						ctx,
						"reader database connection closed with TLS closeNotify warning (connection already closed)",
						zap.Error(err),
					)
				} else {
					log.Error(ctx, "failed to close reader database connection", zap.Error(err))
					errs = append(errs, err)
				}
			} else {
				log.Info(ctx, "reader database connection closed successfully")
			}
		}
		dbReader = nil
	}

	// Return first error if any occurred
	if len(errs) > 0 {
		return errs[0]
	}

	log.Info(ctx, "all database connections closed successfully")
	return nil
}

// CloseWithTimeout closes database connections with a timeout context
func CloseWithTimeout(ctx context.Context, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	return Close(ctx)
}

// isTLSCloseNotifyError checks if the error is the benign TLS closeNotify error
// that occurs when the connection is already closed but TLS tries to send a close alert
func isTLSCloseNotifyError(err error) bool {
	errStr := err.Error()
	return strings.Contains(errStr, "closeNotify alert") &&
		strings.Contains(errStr, "connection was closed anyway") &&
		strings.Contains(errStr, "broken pipe")
}
