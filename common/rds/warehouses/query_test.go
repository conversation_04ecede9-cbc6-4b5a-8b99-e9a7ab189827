package warehouse

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func TestLiveGetWarehouseByAddress(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestLiveGetWarehouseByAddress: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)

	// Create a scheduling integration for the service to allow warehouse access
	integration := rds.CreateTestIntegrationByType(ctx, t, service.ID, models.Scheduling)

	// Create test warehouses with different addresses
	warehouses := []models.Warehouse{
		{
			WarehouseID:           "wh1",
			WarehouseName:         "Exact Match Warehouse",
			WarehouseAddressLine1: "123 Main Street",
			WarehouseFullAddress:  "123 Main Street, New York, NY 10001",
			Source:                models.WarehouseSource(integration.Name),
		},
		{
			WarehouseID:           "wh2",
			WarehouseName:         "Partial Match Warehouse",
			WarehouseAddressLine1: "456 Oak Avenue",
			WarehouseFullAddress:  "456 Oak Avenue, Los Angeles, CA 90210",
			Source:                models.WarehouseSource(integration.Name),
		},
		{
			WarehouseID:           "wh3",
			WarehouseName:         "Different Street Number",
			WarehouseAddressLine1: "789 Oak Avenue",
			WarehouseFullAddress:  "789 Oak Avenue, Los Angeles, CA 90210",
			Source:                models.WarehouseSource(integration.Name),
		},
		{
			WarehouseID:           "wh4",
			WarehouseName:         "No Match Warehouse",
			WarehouseAddressLine1: "999 Pine Road",
			WarehouseFullAddress:  "999 Pine Road, Chicago, IL 60601",
			Source:                models.WarehouseSource(integration.Name),
		},
	}

	// Create warehouses in DB
	for i := range warehouses {
		err := Upsert(ctx, &warehouses[i])
		require.NoError(t, err)
		require.NotZero(t, warehouses[i].ID)
	}

	t.Run("Address matches 100%", func(t *testing.T) {
		result, err := GetWarehouseByAddress(
			ctx,
			service.ID,
			"123 Main Street, New York, NY 10001",
			"123",
		)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "wh1", result.WarehouseID)
		assert.Equal(t, "Exact Match Warehouse", result.WarehouseName)
	})

	t.Run("Address matches 0%", func(t *testing.T) {
		result, err := GetWarehouseByAddress(
			ctx,
			service.ID,
			"555 Nonexistent Boulevard, Unknown City, XX 00000",
			"555",
		)
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.Nil(t, result)
	})

	t.Run("Address matches partially and street number fully", func(t *testing.T) {
		// Search for Oak Avenue with street number 456 - should match wh2 specifically
		result, err := GetWarehouseByAddress(
			ctx,
			service.ID,
			"Oak Ave, Los Angeles",
			"456",
		)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "wh2", result.WarehouseID)
		assert.Equal(t, "Partial Match Warehouse", result.WarehouseName)
		assert.Contains(t, result.WarehouseAddressLine1, "456")
	})

	t.Run("Address matches partially but street number does not", func(t *testing.T) {
		// Search for Oak Avenue with street number 999 (doesn't match any Oak Avenue warehouse)
		// Should return the first warehouse that matches the partial address
		result, err := GetWarehouseByAddress(
			ctx,
			service.ID,
			"Oak Avenue, Los Angeles",
			"999",
		)
		require.Error(t, err)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.Nil(t, result)

		// Before, the functionwould return one of the Oak Avenue warehouses (wh2 or wh3)
		// Now, function should return no warehouse
		// assert.True(t, result.WarehouseID == "wh2" || result.WarehouseID == "wh3")
		// assert.Contains(t, result.WarehouseFullAddress, "Oak Avenue")
		// // The returned warehouse should NOT have the searched street number (999)
		// assert.NotContains(t, result.WarehouseAddressLine1, "999")
	})

	t.Run("Multiple partial matches with street number preference", func(t *testing.T) {
		// Both wh2 and wh3 match "Oak Avenue", but only wh3 has street number 789
		result, err := GetWarehouseByAddress(
			ctx,
			service.ID,
			"Oak Avenue",
			"789",
		)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "wh3", result.WarehouseID)
		assert.Equal(t, "Different Street Number", result.WarehouseName)
		assert.Contains(t, result.WarehouseAddressLine1, "789")
	})

	t.Run("Empty street number parameter", func(t *testing.T) {
		// When street number is empty, should return first match
		result, err := GetWarehouseByAddress(
			ctx,
			service.ID,
			"Oak Avenue",
			"",
		)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.True(t, result.WarehouseID == "wh2" || result.WarehouseID == "wh3")
		assert.Contains(t, result.WarehouseFullAddress, "Oak Avenue")
	})

	t.Run("Service has no access to warehouses", func(t *testing.T) {
		// Create a service without scheduling integration
		serviceWithoutAccess := rds.CreateTestService(ctx, t, models.Service{Name: "No Access Service"})

		result, err := GetWarehouseByAddress(
			ctx,
			serviceWithoutAccess.ID,
			"123 Main Street, New York, NY 10001",
			"123",
		)
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.Nil(t, result)
	})
}
