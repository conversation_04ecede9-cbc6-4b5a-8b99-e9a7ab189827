package tmscustomerprompts

import (
	"context"
	"errors"
	"fmt"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/redis"
)

func Create(ctx context.Context, prompt *models.TMSCustomerPrompt) error {
	return rds.WithContext(ctx).Create(prompt).Error
}

// UpsertCustomerPrompt creates or updates a customer prompt
func UpsertCustomerPrompt(ctx context.Context, prompt *models.TMSCustomerPrompt) error {
	// save to rds
	err := Create(ctx, prompt)
	if err != nil {
		return err
	}

	var key string
	if prompt.TMSCustomerID == nil {
		key = fmt.Sprintf(TMSPromptKey, prompt.TMSIntegrationID, prompt.Feature, prompt.ExtractorName)
	} else {
		key = fmt.Sprintf(
			"tms_customer_prompt:%d:%d:%s:%s",
			prompt.TMSIntegrationID,
			*prompt.TMSCustomerID,
			prompt.Feature,
			prompt.ExtractorName,
		)
	}

	// delete from redis
	err = redis.DeleteKey(ctx, key)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		return err
	}

	return nil
}

// UpsertTMSWidePrompt creates or updates a TMS-wide prompt
func UpsertTMSWidePrompt(ctx context.Context, prompt *models.TMSCustomerPrompt) error {
	// save to rds
	err := Create(ctx, prompt)
	if err != nil {
		return err
	}

	// delete from redis
	err = redis.DeleteKey(ctx, fmt.Sprintf(TMSPromptKey, prompt.TMSIntegrationID, prompt.Feature, prompt.ExtractorName))
	if err != nil && !errors.Is(err, redis.NilEntry) {
		return err
	}

	return nil
}
