package tmscustomerprompts

import (
	"context"
	"errors"
	"fmt"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/redis"
)

// DeleteCustomerPrompt deletes a customer prompt from rds and redis
func DeleteCustomerPrompt(
	ctx context.Context,
	tmsID uint,
	customerID *uint,
	feature string,
	extractorName string,
) error {
	// delete from rds
	err := rds.WithContext(ctx).
		Where("tms_integration_id = ? AND tms_customer_id = ? AND feature = ? AND extractor_name = ?",
			tmsID, customerID, feature, extractorName).
		Delete(&models.TMSCustomerPrompt{}).Error
	if err != nil {
		return err
	}

	var key string
	if customerID == nil {
		key = fmt.Sprintf(TMSPromptKey, tmsID, feature, extractorName)
	} else {
		key = fmt.Sprintf(
			"tms_customer_prompt:%d:%d:%s:%s",
			tmsID,
			*customerID,
			feature,
			extractorName,
		)
	}

	// delete from redis
	err = redis.DeleteKey(ctx, key)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		return err
	}

	return nil
}

// DeleteTMSWidePrompt deletes a TMS-wide prompt from rds and redis
func DeleteTMSWidePrompt(ctx context.Context, tmsID uint, feature string, extractorName string) error {
	// delete from rds
	err := rds.WithContext(ctx).
		Where(
			"tms_integration_id = ? AND scope = ? AND feature = ? AND extractor_name = ?",
			tmsID,
			models.TMSScope,
			feature,
			extractorName,
		).
		Delete(&models.TMSCustomerPrompt{}).Error
	if err != nil {
		return err
	}

	// delete from redis
	err = redis.DeleteKey(ctx, fmt.Sprintf(TMSPromptKey, tmsID, feature, extractorName))
	if err != nil && !errors.Is(err, redis.NilEntry) {
		return err
	}

	return nil
}
