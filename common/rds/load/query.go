package load

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Gets load and preloads TMS object by *service ID*. Helpful lookup for when we don't know the TMS source yet
func GetLoadByFreightIDAndService(
	ctx context.Context,
	serviceID uint,
	freightTrackingID string,
) (load models.Load, err error) {

	return load, rds.WithContextReader(ctx).
		Where("freight_tracking_id = ? AND service_id = ?", freightTrackingID, serviceID).
		Preload("TMS").
		Preload("PickupWarehouse").
		Preload("DropoffWarehouse").
		First(&load).Error
}

// Gets load by external ID & *service ID*. Helpful lookup for when we don't know the TMS source yet
func GetLoadByExternalTMSIDAndService(
	ctx context.Context,
	serviceID uint,
	externalTMSID string,
) (load models.Load, err error) {

	return load, rds.WithContextReader(ctx).
		Where("external_tms_id = ? AND service_id = ?", externalTMSID, serviceID).
		Preload("TMS").
		First(&load).Error
}

func GetLoadByFreightIDAndTMSID(
	ctx context.Context,
	tmsID uint,
	freightTrackingID string,
) (load models.Load, err error) {

	return load, rds.WithContextReader(ctx).
		Where("freight_tracking_id = ? AND tms_id = ?", freightTrackingID, tmsID).
		First(&load).Error
}

func GetLoadByExternalTMSIDAndTMSID(
	ctx context.Context,
	tmsID uint,
	externalTMSID string,
) (load models.Load, err error) {

	return load, rds.WithContextReader(ctx).
		Where("external_tms_id = ? AND tms_id = ?", externalTMSID, tmsID).
		Preload("PickupWarehouse").
		Preload("DropoffWarehouse").
		First(&load).Error
}

func GetLoadBySearchQuery(
	ctx context.Context,
	queryMap map[string]string,
	scopeMap map[string]string,
	serviceID uint,
) (loads []models.Load, err error) {
	var loadSearchThreshold = 0.1

	return loads, rds.WithContextReader(ctx).
		Scopes(
			fuzzyMatchByField(ctx, "customer_name", scopeMap["customer_name"], loadSearchThreshold),
			fuzzyMatchByField(ctx, "pickup_city", scopeMap["pickup_city"], loadSearchThreshold),
			fuzzyMatchByField(ctx, "consignee_city", scopeMap["dropoff_city"], loadSearchThreshold),
			PickupDateMatch(scopeMap["pickup_appt"]),
			DropoffDateMatch(scopeMap["dropoff_appt"]),
		).
		Where(queryMap). // Filter by pickup/dropoff states
		Where("service_id = ?", serviceID).
		Order("created_at DESC").
		Preload("TMS").
		Limit(10).
		Find(&loads).Error
}

func GetLoadByID(ctx context.Context, id uint) (load models.Load, err error) {
	return load, rds.WithContextReader(ctx).
		Where("id = ?", id).
		Preload("TMS").
		First(&load).Error
}

func GetLoadIDsByService(ctx context.Context, tmsID uint) (loadIDs []string, err error) {
	return loadIDs, rds.WithContextReader(ctx).
		Model(&models.Load{}).
		Where("tms_id = ?", tmsID).
		Select("freight_tracking_id").
		Find(&loadIDs).Error
}

// Gets highest externalTMSID for a given TMS, assuming TMS's IDs are numeric and sequentially incrementing
func GetHighestExternalTMSID(ctx context.Context, integrationID uint) (externalTMSID int, err error) {
	var highestID sql.NullInt32

	err = rds.WithContextReader(ctx).Model(&models.Load{}).
		Select("MAX(CAST(external_tms_id AS integer))").
		Where("tms_id = ? AND external_tms_id ~ '^\\d+$'", integrationID).
		Scan(&highestID).Error
	if err != nil {
		return 0, fmt.Errorf("error fetching highest external TMS id: %w", err)
	}

	if highestID.Int32 == 0 {
		return 0, nil
	}

	return int(highestID.Int32), nil
}

func GetHighestFreightTrackingID(ctx context.Context, integrationID uint) (externalTMSID int, err error) {
	var highestID sql.NullInt32

	err = rds.WithContextReader(ctx).Model(&models.Load{}).
		Select("MAX(CAST(freight_tracking_id AS integer))").
		Where("tms_id = ? AND freight_tracking_id ~ '^\\d+$'", integrationID).
		Scan(&highestID).Error
	if err != nil {
		return 0, fmt.Errorf("error fetching highest freight tracking id: %w", err)
	}

	if highestID.Int32 == 0 {
		return 0, nil
	}

	return int(highestID.Int32), nil
}

func ListFreightTrackingIDs(ctx context.Context, integrationID uint) ([]string, error) {
	var freightTrackingIDs []string

	err := rds.WithContextReader(ctx).Model(&models.Load{}).
		Select("freight_tracking_id").
		Where("tms_id = ?", integrationID).
		Scan(&freightTrackingIDs).Error

	return freightTrackingIDs, err
}

func ListExternalTMSIDs(ctx context.Context, integrationID uint) ([]string, error) {
	var ids []string

	err := rds.WithContextReader(ctx).Model(&models.Load{}).
		Select("external_tms_id").
		Where("tms_id = ?", integrationID).
		Scan(&ids).Error

	return ids, err
}

func PickupDateMatch(pickupDate string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if pickupDate == "" {
			return db
		}
		return db.
			Where(
				"date_trunc('day', pickup_appt_start_time) = date_trunc('day', ?::TIMESTAMP)",
				pickupDate,
			)
	}
}

func DropoffDateMatch(dropoffDate string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if dropoffDate == "" {
			return db
		}
		return db.
			Where(
				"date_trunc('day', consignee_appt_start_time) = date_trunc('day', ?::TIMESTAMP)",
				dropoffDate,
			)
	}
}

func GetLoadByCustomerRef(ctx context.Context, customRef string, serviceID uint) (load *models.Load, err error) {
	return load, rds.WithContextReader(ctx).
		Where("customer_ref_number = ? AND service_id = ?", customRef, serviceID).
		First(&load).Error
}

func GetLoadByPONum(ctx context.Context, poNum string, serviceID uint) (load *models.Load, err error) {
	return load, rds.WithContextReader(ctx).
		Where("po_nums = ? AND service_id = ?", poNum, serviceID).
		First(&load).Error
}

func GetListOfUniqueCustomers(ctx context.Context, tmsID uint) (uniqueCustomers []models.TMSCustomer, err error) {
	err = rds.WithContextReader(ctx).Table("loads").
		Select("DISTINCT ON (customer_external_tms_id) customer_external_tms_id as external_id",
			"customer_name as name").
		Where("tms_id = ?", tmsID).
		Find(&uniqueCustomers).Error

	return
}

// Helper function to fuzzy match a load by a column name
func fuzzyMatchByField(
	ctx context.Context,
	columnName,
	searchTerm string,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if searchTerm == "" {
			return db
		}

		return db.Scopes(func(tx *gorm.DB) *gorm.DB {
			if err := rds.SetSimilarityThreshold(ctx, tx, similarityThreshold); err != nil {
				log.Errorf(ctx, "failed to set similarity threshold: %w", err)
				return db
			}

			return tx.
				// e.g. customer_name % @searchTerm OR customer_name ILIKE '%@searchTerm%'
				// %% is to escape % in the Postgres query
				Where(fmt.Sprintf(`%[1]s %% @searchTerm OR %[1]s ILIKE '%%' || @searchTerm || '%%'`, columnName),
					sql.Named("searchTerm", searchTerm))
		})
	}
}

// GetOrdersForLoad retrieves all orders associated with a load
func GetOrdersForLoad(ctx context.Context, loadID uint, externalLoadID string) ([]models.Order, error) {
	var orders []models.Order

	// Query orders directly using the LoadID field
	err := rds.WithContextReader(ctx).
		Where("(load_id = ? OR external_load_id = ?)", loadID, externalLoadID).
		Find(&orders).Error
	if err != nil {
		return nil, err
	}

	return orders, nil
}

// FindLoadIDsWithEmptyCarrierCost returns freight_tracking_ids for loads that
// don't have ratedata_carrier_cost populated
func FindLoadIDsWithEmptyCarrierCost(ctx context.Context, tmsID uint) ([]string, error) {
	var loadIDs []string

	err := rds.WithContextReader(ctx).Model(&models.Load{}).
		Select("freight_tracking_id").
		Where("tms_id = ?", tmsID).
		Where("ratedata_carrier_cost IS NULL OR ratedata_carrier_cost = 0").
		Scan(&loadIDs).Error

	return loadIDs, err
}

// FindLoadIDsWithEmptyCarrierCostAndRecentPickup returns freight_tracking_ids for loads that
// don't have ratedata_carrier_cost populated and have a pickup_date within the last 4 weeks
func FindLoadIDsWithEmptyCarrierCostAndRecentPickup(ctx context.Context, tmsID uint) ([]string, error) {
	var loadIDs []string
	var cutoffTime = time.Now().Add(-4 * 7 * 24 * time.Hour)

	err := rds.WithContextReader(ctx).Model(&models.Load{}).
		Select("freight_tracking_id").
		Where("tms_id = ?", tmsID).
		Where("ratedata_carrier_cost IS NULL OR ratedata_carrier_cost = 0").
		Where("pickup_date > ?", cutoffTime).
		Scan(&loadIDs).Error

	return loadIDs, err
}

// FindRecentlyUpdatedLoadIDs returns freight_tracking_ids for loads that were updated
// within the given timeframe
func FindRecentlyUpdatedLoadIDs(ctx context.Context, tmsID uint, minutes int) ([]string, error) {
	var loadIDs []string
	cutoffTime := time.Now().Add(-time.Duration(minutes) * time.Minute)

	err := rds.WithContextReader(ctx).Model(&models.Load{}).
		Select("freight_tracking_id").
		Where("tms_id = ?", tmsID).
		Where("updated_at > ?", cutoffTime).
		Scan(&loadIDs).Error

	return loadIDs, err
}

// GetLoadByWarehouseID gets a load by warehouse ID, request type, and service ID
func GetLoadByWarehouseID(
	ctx context.Context,
	serviceID uint,
	warehouseID uint,
	requestType models.RequestType,
) (load models.Load, err error) {

	query := rds.WithContext(ctx)

	switch requestType {
	case models.RequestTypePickup:
		query = query.
			Where("pickup_warehouse_id = ? AND service_id = ?", warehouseID, serviceID).
			Preload("PickupWarehouse")

	case models.RequestTypeDropoff:
		query = query.
			Where("dropoff_warehouse_id = ? AND service_id = ?", warehouseID, serviceID).
			Preload("DropoffWarehouse")

	default:
		return load, fmt.Errorf("invalid request type: %s", requestType)
	}

	return load, query.First(&load).Error
}

func GetLoadsByThreadID(ctx context.Context, threadID string) ([]models.Load, error) {
	loads := []models.Load{}

	err := rds.WithContextReader(ctx).
		Where(
			"id IN (SELECT load_id FROM email_loads WHERE email_id IN (SELECT id FROM emails WHERE thread_id = ?))",
			threadID,
		).
		Find(&loads).Error

	return loads, err
}

// GetRecentLoadReferenceInfoForTMSCustomer gets the recent loads for a TMS customer
func GetRecentLoadReferenceInfoForTMSCustomer(
	ctx context.Context,
	tmsID uint,
	customerName string,
	limit int,
) ([]models.LoadReferenceInfo, error) {

	if tmsID == 0 {
		return nil, errors.New("tmsID must be greater than 0")
	}

	if customerName == "" {
		return nil, errors.New("customerName cannot be empty")
	}

	if limit <= 0 {
		limit = 10
	}

	loads := []models.LoadReferenceInfo{}

	err := rds.WithContextReader(ctx).
		Model(&models.Load{}).
		Select(
			"customer_ref_number",
			"po_nums",
			"pickup_ref_number",
			"consignee_ref_number",
		).
		Where("tms_id = ? AND customer_name ILIKE ?", tmsID, customerName).
		Order("created_at DESC").
		Limit(limit).
		Find(&loads).Error

	if err != nil {
		return nil, err
	}

	return loads, nil
}
