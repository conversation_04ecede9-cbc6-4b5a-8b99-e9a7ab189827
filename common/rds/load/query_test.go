package load

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func TestGetRecentLoadReferenceInfoForTMSCustomer(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping test: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := context.Background()
	rds.MustOpenTestDB(ctx, "drumkit_test_db")

	t.Cleanup(func() {
		rds.ClearTestDB(ctx, t)
	})
	rds.ClearTestDB(ctx, t)

	service := models.Service{Name: "Test Service"}
	require.NoError(t, rds.CreateService(ctx, &service))

	tms := models.Integration{
		Name:      models.Aljex,
		Type:      models.TMS,
		ServiceID: service.ID,
	}
	require.NoError(t, rds.WithContext(ctx).Create(&tms).Error)

	customerName := "Test Customer LLC"

	// Create test loads with unique freight_tracking_id values to avoid constraint violations
	baseTime := time.Now().Add(-24 * time.Hour)
	testLoads := []models.Load{
		{
			ServiceID:         service.ID,
			TMSID:             tms.ID,
			FreightTrackingID: "TEST001", // Unique freight tracking ID
			LoadCoreInfo: models.LoadCoreInfo{
				Customer: models.Customer{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Name: customerName,
					},
					RefNumber: "REF001",
				},
				Pickup: models.Pickup{
					RefNumber: "PICKUP001",
				},
				Consignee: models.Consignee{
					RefNumber: "CONSIGNEE001",
				},
			},
		},
		{
			ServiceID:         service.ID,
			TMSID:             tms.ID,
			FreightTrackingID: "TEST002", // Unique freight tracking ID
			LoadCoreInfo: models.LoadCoreInfo{
				Customer: models.Customer{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Name: customerName,
					},
					RefNumber: "REF002",
				},
				Pickup: models.Pickup{
					RefNumber: "PICKUP002",
				},
				Consignee: models.Consignee{
					RefNumber: "CONSIGNEE002",
				},
			},
		},
		{
			ServiceID:         service.ID,
			TMSID:             tms.ID,
			FreightTrackingID: "TEST003", // Unique freight tracking ID
			LoadCoreInfo: models.LoadCoreInfo{
				Customer: models.Customer{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Name: customerName,
					},
					RefNumber: "REF003",
				},
				Pickup: models.Pickup{
					RefNumber: "PICKUP003",
				},
				Consignee: models.Consignee{
					RefNumber: "CONSIGNEE003",
				},
			},
		},
		{
			ServiceID:         service.ID,
			TMSID:             tms.ID,
			FreightTrackingID: "TEST004", // Different customer
			LoadCoreInfo: models.LoadCoreInfo{
				Customer: models.Customer{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Name: "Different Customer",
					},
					RefNumber: "REF004",
				},
				Pickup: models.Pickup{
					RefNumber: "PICKUP004",
				},
				Consignee: models.Consignee{
					RefNumber: "CONSIGNEE004",
				},
			},
		},
	}

	for i, load := range testLoads {
		load.CreatedAt = baseTime.Add(time.Duration(i+1) * time.Hour)
		require.NoError(t, rds.WithContext(ctx).Create(&load).Error)
	}

	t.Run("success - returns recent loads for customer", func(t *testing.T) {
		results, err := GetRecentLoadReferenceInfoForTMSCustomer(ctx, tms.ID, customerName, 5)

		require.NoError(t, err)
		assert.Len(t, results, 3) // Should return 3 loads for "Test Customer LLC"

		// Verify results are sorted by created_at DESC (most recent first)
		assert.Equal(t, "REF003", results[0].CustomerRefNumber)
		assert.Equal(t, "PICKUP003", results[0].PickupRefNumber)
		assert.Equal(t, "CONSIGNEE003", results[0].ConsigneeRefNumber)

		assert.Equal(t, "REF002", results[1].CustomerRefNumber)
		assert.Equal(t, "REF001", results[2].CustomerRefNumber)
	})

	t.Run("success - respects limit parameter", func(t *testing.T) {
		results, err := GetRecentLoadReferenceInfoForTMSCustomer(ctx, tms.ID, customerName, 2)

		require.NoError(t, err)
		assert.Len(t, results, 2) // Should respect the limit

		// Should return the 2 most recent
		assert.Equal(t, "REF003", results[0].CustomerRefNumber)
		assert.Equal(t, "REF002", results[1].CustomerRefNumber)
	})

	t.Run("success - no results for non-existent customer", func(t *testing.T) {
		results, err := GetRecentLoadReferenceInfoForTMSCustomer(ctx, tms.ID, "Non-existent Customer", 10)

		require.NoError(t, err)
		assert.Len(t, results, 0)
	})

	t.Run("error - invalid tmsID", func(t *testing.T) {
		results, err := GetRecentLoadReferenceInfoForTMSCustomer(ctx, 0, customerName, 10)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "tmsID must be greater than 0")
		assert.Nil(t, results)
	})

	t.Run("error - empty customerName", func(t *testing.T) {
		results, err := GetRecentLoadReferenceInfoForTMSCustomer(ctx, tms.ID, "", 10)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "customerName cannot be empty")
		assert.Nil(t, results)
	})
}
