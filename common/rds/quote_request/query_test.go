package quoterequest

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func TestGetQuoteRequestsByFieldSimilarity(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestGetQuoteRequestsByFieldSimilarity: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)

	// Create test service
	service := rds.CreateTestService(ctx, t, models.Service{Name: "Test Brokerage"})

	// Create test users
	user1 := rds.CreateTestUser(ctx, t, service.ID)
	user2 := rds.CreateTestUser(ctx, t, service.ID)

	// Create test emails
	email1 := models.Email{
		ExternalID: "email1",
		ServiceID:  service.ID,
		UserID:     user1.ID,
	}
	err := rds.WithContext(ctx).Create(&email1).Error
	require.NoError(t, err)

	email2 := models.Email{
		ExternalID: "email2",
		ServiceID:  service.ID,
		UserID:     user2.ID,
	}
	err = rds.WithContext(ctx).Create(&email2).Error
	require.NoError(t, err)

	// Create test TMS
	tms := rds.CreateTestIntegrationByType(ctx, t, service.ID, models.TMS)

	// Create test customer
	customer := models.TMSCustomer{
		TMSIntegrationID: tms.ID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: "test-customer-123",
			Name:          "Test Customer Inc",
		},
	}
	err = rds.WithContext(ctx).Create(&customer).Error
	require.NoError(t, err)
	require.NotZero(t, customer.ID)

	// Helper to create a quote request with suggested fields only
	createQuoteRequestWithSuggestedFields := func(
		userID uint,
		emailID uint,
		transportType models.TransportType,
		pickupState, pickupCity, pickupZip string,
		dropoffState, dropoffCity, dropoffZip string,
		pickupDate time.Time,
		sourceExternalID string,
		rfcMessageID string,
	) models.QuoteRequest {
		qr := models.QuoteRequest{
			UserID:           userID,
			ServiceID:        service.ID,
			EmailID:          emailID,
			ThreadID:         "thread1",
			RFCMessageID:     rfcMessageID,
			SourceCategory:   models.EmailSourceCategory,
			Source:           "testsource",
			SourceExternalID: sourceExternalID,
			Status:           models.Pending,
			SuggestedRequest: models.QuoteLoadInfo{
				TransportType:    transportType,
				CustomerID:       customer.ID,
				PickupLocation:   models.Address{State: pickupState, City: pickupCity, Zip: pickupZip},
				DeliveryLocation: models.Address{State: dropoffState, City: dropoffCity, Zip: dropoffZip},
				PickupDate:       models.NullTime{Valid: true, Time: pickupDate},
			},
		}

		err := CreateQuoteRequest(ctx, &qr)
		require.NoError(t, err)
		require.NotZero(t, qr.ID)

		return qr
	}

	// Helper to create a quote request with applied fields
	createQuoteRequestWithAppliedFields := func(
		userID uint,
		emailID uint,
		transportType models.TransportType,
		pickupState, pickupCity, pickupZip string,
		dropoffState, dropoffCity, dropoffZip string,
		pickupDate time.Time,
		sourceExternalID string,
		rfcMessageID string,
	) models.QuoteRequest {
		qr := models.QuoteRequest{
			UserID:           userID,
			ServiceID:        service.ID,
			EmailID:          emailID,
			ThreadID:         "thread1",
			RFCMessageID:     rfcMessageID,
			SourceCategory:   models.EmailSourceCategory,
			Source:           "testsource",
			SourceExternalID: sourceExternalID,
			Status:           models.Pending,
			AppliedRequest: models.QuoteLoadInfo{
				TransportType:    transportType,
				CustomerID:       customer.ID,
				PickupLocation:   models.Address{State: pickupState, City: pickupCity, Zip: pickupZip},
				DeliveryLocation: models.Address{State: dropoffState, City: dropoffCity, Zip: dropoffZip},
				PickupDate:       models.NullTime{Valid: true, Time: pickupDate},
			},
		}

		err := CreateQuoteRequest(ctx, &qr)
		require.NoError(t, err)
		require.NotZero(t, qr.ID)

		return qr
	}

	// Helper to create a load
	createTestLoad := func(
		externalID string,
		transportType models.TransportType,
		useTransportTypeEnum bool,
		pickupState, pickupCity, pickupZip string,
		dropoffState, dropoffCity, dropoffZip string,
		pickupDate time.Time,
	) models.Load {
		load := models.Load{
			ServiceID:         service.ID,
			TMSID:             tms.ID,
			ExternalTMSID:     externalID,
			FreightTrackingID: externalID,
			LoadCoreInfo: models.LoadCoreInfo{
				Status: "open",
				Customer: models.Customer{
					CompanyCoreInfo: models.CompanyCoreInfo{
						ExternalTMSID: customer.ExternalTMSID,
					},
				},
				Pickup: models.Pickup{
					CompanyCoreInfo: models.CompanyCoreInfo{
						State:   pickupState,
						City:    pickupCity,
						Zipcode: pickupZip,
					},
					ReadyTime: models.NullTime{Valid: true, Time: pickupDate},
				},
				Consignee: models.Consignee{
					CompanyCoreInfo: models.CompanyCoreInfo{
						State:   dropoffState,
						City:    dropoffCity,
						Zipcode: dropoffZip,
					},
				},
				Specifications: models.Specifications{
					TransportTypeEnum: helpers.Ternary(useTransportTypeEnum, &transportType, nil),
					TransportType:     string(transportType),
				},
			},
		}

		err := rds.WithContext(ctx).Create(&load).Error
		require.NoError(t, err)
		require.NotZero(t, load.ID)

		return load
	}

	baseTime := time.Now().Add(-12 * time.Hour) // Within lookup window

	t.Run("match by suggested fields", func(t *testing.T) {
		// Clear any existing quote requests
		err := rds.WithContext(ctx).Where("service_id = ?", service.ID).Delete(&models.QuoteRequest{}).Error
		require.NoError(t, err)

		// Create a quote request with only suggested fields populated
		qr := createQuoteRequestWithSuggestedFields(
			user1.ID,
			email1.ID,
			models.VanTransportType,
			"CA", "Los Angeles", "90210",
			"NY", "New York", "10001",
			baseTime,
			"van-qr-suggested",
			"rfc-message-suggested",
		)

		// Create a load that should match by suggested fields
		load := createTestLoad(
			"test-load-suggested",
			models.VanTransportType,
			true,
			"CA", "Los Angeles", "90210",
			"NY", "New York", "10001",
			baseTime.Add(6*time.Hour),
		)

		// Test the function
		matches, err := GetQuoteRequestsByFieldSimilarity(ctx, load)
		require.NoError(t, err)
		require.Len(t, matches, 1)
		assert.Equal(t, qr.ID, matches[0].ID)
	})

	t.Run("match by applied fields", func(t *testing.T) {
		// Clear any existing quote requests
		err := rds.WithContext(ctx).Where("service_id = ?", service.ID).Delete(&models.QuoteRequest{}).Error
		require.NoError(t, err)

		// Create a quote request with applied fields populated (higher priority than suggested)
		qr := createQuoteRequestWithAppliedFields(
			user1.ID,
			email1.ID,
			models.VanTransportType,
			"CA", "Los Angeles", "90210",
			"NY", "New York", "10001",
			baseTime,
			"van-qr-applied",
			"rfc-message-applied",
		)

		// Also create a quote request with only suggested fields to ensure applied takes precedence
		createQuoteRequestWithSuggestedFields(
			user2.ID,
			email2.ID,
			models.VanTransportType,
			"CA", "Los Angeles", "90210",
			"NY", "New York", "10001",
			baseTime.Add(1*time.Hour),
			"van-qr-suggested-lower-priority",
			"rfc-message-suggested-lower",
		)

		// Create a load that should match by applied fields
		load := createTestLoad(
			"test-load-applied",
			models.VanTransportType,
			true,
			"CA", "Los Angeles", "90210",
			"NY", "New York", "10001",
			baseTime.Add(6*time.Hour),
		)

		// Test the function - should prefer applied fields match
		matches, err := GetQuoteRequestsByFieldSimilarity(ctx, load)
		require.NoError(t, err)
		require.Len(t, matches, 1)
		assert.Equal(t, qr.ID, matches[0].ID)
	})

	t.Run("multiple matches from same email to different users", func(t *testing.T) {
		// Clear any existing quote requests
		err := rds.WithContext(ctx).Where("service_id = ?", service.ID).Delete(&models.QuoteRequest{}).Error
		require.NoError(t, err)

		// Create duplicate quote requests from the same email (same RFCMessageID) to different users
		// This simulates when the same email is received by multiple users in a shared inbox
		rfcMessageID := "rfc-message-duplicate"

		qr1 := createQuoteRequestWithSuggestedFields(
			user1.ID,
			email1.ID,
			models.VanTransportType,
			"CA", "Los Angeles", "90210",
			"NY", "New York", "10001",
			baseTime,
			"van-qr-duplicate-user1",
			rfcMessageID,
		)

		qr2 := createQuoteRequestWithSuggestedFields(
			user2.ID,
			email2.ID,
			models.VanTransportType,
			"CA", "Los Angeles", "90210",
			"NY", "New York", "10001",
			baseTime.Add(1*time.Minute), // Slightly different time but same RFC message
			"van-qr-duplicate-user2",
			rfcMessageID,
		)

		// Create a load that should match both quote requests
		load := createTestLoad(
			"test-load-duplicates",
			models.VanTransportType,
			true,
			"CA", "Los Angeles", "90210",
			"NY", "New York", "10001",
			baseTime.Add(6*time.Hour),
		)

		// Test the function - should return both matching quote requests
		matches, err := GetQuoteRequestsByFieldSimilarity(ctx, load)
		require.NoError(t, err)
		require.Len(t, matches, 2)

		// Verify both quote requests are returned
		foundIDs := make(map[uint]bool)
		for _, match := range matches {
			foundIDs[match.ID] = true
			assert.Equal(t, rfcMessageID, match.RFCMessageID)
		}

		assert.True(t, foundIDs[qr1.ID], "Should find first quote request")
		assert.True(t, foundIDs[qr2.ID], "Should find second quote request")
	})

	t.Run("multiple, different QRs in same email to same user, should match only 1 QR", func(t *testing.T) {
		// Clear any existing quote requests
		err := rds.WithContext(ctx).Where("service_id = ?", service.ID).Delete(&models.QuoteRequest{}).Error
		require.NoError(t, err)

		qr1 := createQuoteRequestWithSuggestedFields(
			user1.ID,
			email1.ID,
			models.VanTransportType,
			"CA", "Los Angeles", "90210",
			"NY", "New York", "10001",
			baseTime,
			"email-123",
			"rfc-message-123",
		)

		_ = createQuoteRequestWithSuggestedFields(
			user1.ID,
			email1.ID,
			models.VanTransportType,
			"MA", "Boston", "02101",
			"MA", "Cambridge", "02138",
			baseTime,
			"email-123",
			"rfc-message-123",
		)

		load := createTestLoad(
			"test-load-123",
			models.VanTransportType,
			true,
			"CA", "Los Angeles", "90210",
			"NY", "New York", "10001",
			baseTime.Add(6*time.Hour),
		)

		matches, err := GetQuoteRequestsByFieldSimilarity(ctx, load)
		require.NoError(t, err)
		require.Len(t, matches, 1)
		assert.Equal(t, qr1.ID, matches[0].ID)
	})
	t.Run("no matches", func(t *testing.T) {
		// Clear any existing quote requests
		err := rds.WithContext(ctx).Where("service_id = ?", service.ID).Delete(&models.QuoteRequest{}).Error
		require.NoError(t, err)

		_ = createQuoteRequestWithSuggestedFields(
			user1.ID,
			email1.ID,
			models.VanTransportType,
			"MA", "Boston", "02101",
			"MA", "Cambridge", "02138",
			baseTime,
			"van-qr-no-matches",
			"rfc-message-no-matches",
		)

		_ = createQuoteRequestWithSuggestedFields(
			user2.ID,
			email2.ID,
			models.VanTransportType,
			"NY", "Syracuse", "13204",
			"NY", "Albany", "12201",
			baseTime.Add(1*time.Hour),
			"van-qr-no-matches-2",
			"rfc-message-no-matches-2",
		)

		// Create a load that should not match any quote requests
		load := createTestLoad(
			"test-load-no-matches",
			models.VanTransportType,
			true,
			"CA", "Los Angeles", "90210",
			"NY", "New York", "10001",
			baseTime.Add(6*time.Hour),
		)

		// Test the function - should return no matches
		matches, err := GetQuoteRequestsByFieldSimilarity(ctx, load)
		require.Error(t, err)
		require.ErrorIs(t, err, gorm.ErrRecordNotFound)
		require.Len(t, matches, 0)
	})
}

func TestFindQuoteRequestsMatchingLoad(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestFindQuoteRequestsMatchingLoad: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)

	// Create test service
	service := rds.CreateTestService(ctx, t, models.Service{Name: "Test Brokerage"})

	// Create test user
	user := rds.CreateTestUser(ctx, t, service.ID)

	// Create test email
	email := rds.CreateTestEmail(ctx, t, service.ID, user.ID)

	// Create test TMS
	tms := rds.CreateTestIntegrationByType(ctx, t, service.ID, models.TMS)

	// Test helper to create a quote request with suggested values only
	createQuoteRequestWithSuggestedValues := func(
		transportType models.TransportType,
		pickupState, pickupCity, pickupZip string,
		dropoffState, dropoffCity, dropoffZip string,
		pickupDate time.Time,
		sourceExternalID string,
	) models.QuoteRequest {
		qr := models.QuoteRequest{
			UserID:           user.ID,
			ServiceID:        service.ID,
			EmailID:          email.ID,
			ThreadID:         "thread1",
			SourceCategory:   models.EmailSourceCategory,
			Source:           "testsource",
			SourceExternalID: sourceExternalID,
			Status:           models.Pending,
			SuggestedRequest: models.QuoteLoadInfo{
				TransportType:    transportType,
				PickupLocation:   models.Address{State: pickupState, City: pickupCity, Zip: pickupZip},
				DeliveryLocation: models.Address{State: dropoffState, City: dropoffCity, Zip: dropoffZip},
				PickupDate:       models.NullTime{Valid: true, Time: pickupDate},
			},
		}

		err := CreateQuoteRequest(ctx, &qr)
		require.NoError(t, err)
		require.NotZero(t, qr.ID)

		return qr
	}

	// Test helper to create a load
	// NOTE: Test load MUST be created after QRs due to condition
	// `Where("created_at BETWEEN ? AND ?", newLoadCreatedAt.Add(-WonLoadLookupWindow), newLoadCreatedAt)`
	createTestLoad := func(
		externalID string,
		transportType models.TransportType,
		useTransportTypeEnum bool,
		pickupState, pickupCity, pickupZip string,
		dropoffState, dropoffCity, dropoffZip string,
		pickupDate time.Time,
	) models.Load {
		load := models.Load{
			ServiceID:         service.ID,
			TMSID:             tms.ID,
			ExternalTMSID:     externalID,
			FreightTrackingID: externalID,
			LoadCoreInfo: models.LoadCoreInfo{
				Status: "open",
				Pickup: models.Pickup{
					CompanyCoreInfo: models.CompanyCoreInfo{
						State:   pickupState,
						City:    pickupCity,
						Zipcode: pickupZip,
					},
					ReadyTime: models.NullTime{Valid: true, Time: pickupDate},
				},
				Consignee: models.Consignee{
					CompanyCoreInfo: models.CompanyCoreInfo{
						State:   dropoffState,
						City:    dropoffCity,
						Zipcode: dropoffZip,
					},
				},
				Specifications: models.Specifications{
					TransportTypeEnum: helpers.Ternary(useTransportTypeEnum, &transportType, nil),
					TransportType:     string(transportType),
				},
			},
		}

		err := rds.WithContext(ctx).Create(&load).Error
		require.NoError(t, err)
		require.NotZero(t, load.ID)

		return load
	}

	t.Run("simple cases", func(t *testing.T) {
		// Setup: Create 5 quote requests with varying transport types
		baseTime := time.Now().Add(-12 * time.Hour) // Within lookup window
		t.Run("load with matches by suggested fields - multiple VAN quote requests", func(t *testing.T) {
			// 2 VAN quote requests that should match
			vanQR1 := createQuoteRequestWithSuggestedValues(
				models.VanTransportType,
				"CA", "Los Angeles", "90210",
				"NY", "New York", "10001",
				baseTime,
				"van-qr-1",
			)

			vanQR2 := createQuoteRequestWithSuggestedValues(
				models.VanTransportType,
				"ca", "los angeles", "90210", // Test case-insensitive matching
				"ny", "new york", "10001",
				baseTime.Add(1*time.Hour),
				"van-qr-2",
			)

			// 1 FLATBED quote request
			createQuoteRequestWithSuggestedValues(
				models.FlatbedTransportType,
				"TX", "Dallas", "75201",
				"FL", "Miami", "33101",
				baseTime.Add(2*time.Hour),
				"flatbed-qr-1",
			)

			// 1 HOTSHOT quote request
			createQuoteRequestWithSuggestedValues(
				models.HotShotTransportType,
				"CO", "Denver", "80201",
				"UT", "Salt Lake City", "84101",
				baseTime.Add(4*time.Hour),
				"hotshot-qr-1",
			)

			// Create a VAN load that should match the 2 VAN quote requests
			load := createTestLoad(
				"test-load-001",
				models.VanTransportType,
				true,
				"CA", "Los Angeles", "90210",
				"NY", "New York", "10001",
				baseTime.Add(6*time.Hour), // Pickup within date range
			)

			// Test findQuoteRequestsMatchingLoad with useAppliedFields=false (suggested values)
			matchingQRs, err := findQuoteRequestsMatchingLoad(ctx, load, false)
			require.NoError(t, err)

			// Should find the 2 VAN quote requests
			assert.Len(t, matchingQRs, 2)

			// Verify the correct quote requests were returned (can't use ElementsMatch)
			foundIDs := make(map[uint]bool)
			for _, qr := range matchingQRs {
				foundIDs[qr.ID] = true
			}

			assert.True(t, foundIDs[vanQR1.ID], "Should find first VAN quote request")
			assert.True(t, foundIDs[vanQR2.ID], "Should find second VAN quote request")

			// Verify all returned quote requests have the correct transport type
			for _, qr := range matchingQRs {
				assert.Equal(t, models.VanTransportType, qr.SuggestedRequest.TransportType)
			}
		})

		t.Run("load with no matches by suggested fields - REEFER transport type", func(t *testing.T) {
			baseTime := time.Now().Add(-12 * time.Hour) // Within lookup window

			// Create a REEFER load that should NOT match any quote requests
			load := createTestLoad(
				"test-load-002",
				models.ReeferTransportType,
				true,
				"CA", "Los Angeles", "90210",
				"NY", "New York", "10001",
				baseTime.Add(5*time.Hour),
			)

			// Test findQuoteRequestsMatchingLoad with useAppliedFields=false (suggested values)
			matchingQRs, err := findQuoteRequestsMatchingLoad(ctx, load, false)
			require.NoError(t, err)

			// Should find no matching quote requests
			assert.Len(t, matchingQRs, 0, "Should not find any matching quote requests for REEFER load")
		})
	})

	t.Run("edge cases", func(t *testing.T) {
		// Clear previous test data
		err := rds.WithContext(ctx).Where("service_id = ?", service.ID).Delete(&models.QuoteRequest{}).Error
		require.NoError(t, err)

		baseTime := time.Now()

		t.Run("quote request outside time window", func(t *testing.T) {
			// Create quote request outside the WonLoadLookupWindow (30 days)
			oldTime := baseTime.Add(-35 * 24 * time.Hour) // 35 days ago, outside window

			quoteRequest := createQuoteRequestWithSuggestedValues(
				models.VanTransportType,
				"CA", "Los Angeles", "90210",
				"NY", "New York", "10001",
				oldTime,
				"old-van-qr",
			)

			// Create load now
			load := createTestLoad(
				"test-load-003",
				models.VanTransportType,
				true,
				"CA", "Los Angeles", "90210",
				"NY", "New York", "10001",
				baseTime,
			)

			// Override Gorm's auto-assigned CreatedAt
			quoteRequest.CreatedAt = oldTime
			err := rds.WithContext(ctx).Save(&quoteRequest).Error
			require.NoError(t, err)

			matchingQRs, err := findQuoteRequestsMatchingLoad(ctx, load, false)
			require.NoError(t, err)

			// Should not find the old quote request
			assert.Len(t, matchingQRs, 0, "Should not find quote requests outside time window")
		})

		t.Run("quote request with won_load_id should be excluded", func(t *testing.T) {
			// Clear previous test data
			err := rds.WithContext(ctx).Where("service_id = ?", service.ID).Delete(&models.QuoteRequest{}).Error
			require.NoError(t, err)

			// Create a quote request that already has a won load
			qr := createQuoteRequestWithSuggestedValues(
				models.VanTransportType,
				"CA", "Los Angeles", "90210",
				"NY", "New York", "10001",
				baseTime.Add(-1*time.Hour),
				"won-van-qr",
			)

			// Update it to have a won_load_id
			wonLoadID := uint(999)
			err = rds.WithContext(ctx).Model(&qr).Update("won_load_id", wonLoadID).Error
			require.NoError(t, err)

			// Create load
			load := createTestLoad(
				"test-load-004",
				models.VanTransportType,
				true,
				"CA", "Los Angeles", "90210",
				"NY", "New York", "10001",
				baseTime,
			)

			matchingQRs, err := findQuoteRequestsMatchingLoad(ctx, load, false)
			require.NoError(t, err)

			// Should not find the quote request that already won a load
			assert.Len(t, matchingQRs, 0, "Should not find quote requests that already won a load")
		})

		t.Run("load with matches by applied fields", func(t *testing.T) {
			// Clear previous test data
			err := rds.WithContext(ctx).Where("service_id = ?", service.ID).Delete(&models.QuoteRequest{}).Error
			require.NoError(t, err)

			baseTime := time.Now().Add(-12 * time.Hour) // Within lookup window

			// Setup
			// Matching VAN QR
			vanQR1 := models.QuoteRequest{
				UserID:         user.ID,
				ServiceID:      service.ID,
				EmailID:        email.ID,
				ThreadID:       "thread1",
				SourceCategory: models.EmailSourceCategory,
				Status:         models.Pending,
				AppliedRequest: models.QuoteLoadInfo{
					PickupDate:    models.NullTime{Valid: true, Time: baseTime.Add(6 * time.Hour)},
					TransportType: models.VanTransportType,
					PickupLocation: models.Address{
						State: "CA", City: "Los Angeles", Zip: "90210",
					},
					DeliveryLocation: models.Address{
						State: "NY", City: "New York", Zip: "10001",
					},
				},
			}

			// Non-matching VAN QR -- suggested fields match but applied fields don't
			vanQR2 := models.QuoteRequest{
				UserID:         user.ID,
				ServiceID:      service.ID,
				EmailID:        email.ID,
				ThreadID:       "thread1",
				SourceCategory: models.EmailSourceCategory,
				Status:         models.Pending,
				SuggestedRequest: models.QuoteLoadInfo{
					PickupDate:    models.NullTime{Valid: true, Time: baseTime.Add(6 * time.Hour)},
					TransportType: models.VanTransportType,
					PickupLocation: models.Address{
						State: "CA", City: "Los Angeles", Zip: "90210",
					},
					DeliveryLocation: models.Address{
						State: "NY", City: "New York", Zip: "10001",
					},
				},
				// Applied fields don't match the load
				AppliedRequest: models.QuoteLoadInfo{
					PickupDate:    models.NullTime{Valid: true, Time: baseTime.Add(6 * time.Hour)},
					TransportType: models.VanTransportType,
					PickupLocation: models.Address{
						State: "MA", City: "Boston", Zip: "02101",
					},
					DeliveryLocation: models.Address{
						State: "NH", City: "Manchester", Zip: "03101",
					},
				},
			}

			// Insert the quote requests into DB
			err = CreateQuoteRequest(ctx, &vanQR1)
			require.NoError(t, err)
			require.NotZero(t, vanQR1.ID)

			err = CreateQuoteRequest(ctx, &vanQR2)
			require.NoError(t, err)
			require.NotZero(t, vanQR2.ID)

			// Create a VAN load that should match the 2 VAN quote requests
			load := createTestLoad(
				"test-load-005",
				models.VanTransportType,
				true,
				"CA", "Los Angeles", "90210",
				"NY", "New York", "10001",
				baseTime.Add(6*time.Hour), // Pickup within date range
			)

			// Test findQuoteRequestsMatchingLoad with useAppliedFields=true (applied values)
			matchingQRs, err := findQuoteRequestsMatchingLoad(ctx, load, true)
			require.NoError(t, err)

			// Should find the 2 VAN quote requests
			assert.Len(t, matchingQRs, 1)

			// Verify the correct quote requests were returned (can't use ElementsMatch)
			foundIDs := make(map[uint]bool)
			for _, qr := range matchingQRs {
				foundIDs[qr.ID] = true
			}

			assert.True(t, foundIDs[vanQR1.ID], "Should find first VAN quote request")

			// Verify all returned quote requests have the correct transport type
			for _, qr := range matchingQRs {
				assert.Equal(t, models.VanTransportType, qr.AppliedRequest.TransportType)
			}
		})

		t.Run("handle no transport type enum, custom TMS transport type", func(t *testing.T) {
			// Clear previous test data
			err := rds.WithContext(ctx).Where("service_id = ?", service.ID).Delete(&models.QuoteRequest{}).Error
			require.NoError(t, err)

			// Create a quote request with a custom transport type
			vanQR := createQuoteRequestWithSuggestedValues(
				models.VanTransportType,
				"CA", "Los Angeles", "90210",
				"NY", "New York", "10001",
				baseTime.Add(6*time.Hour),
				"van-qr-1",
			)

			// Create a VAN load that should match the VAN quote request
			load := createTestLoad(
				"test-load-006",
				// Custom TMS transport type; this part of the query is being tested here:
				//nolint:lll
				// "(suggested_transport_type ILIKE '%%' || ? || '%%' OR ? ILIKE '%%' || suggested_transport_type || '%%')",
				models.TransportType("VAN (DAT)"),
				false,
				"CA", "Los Angeles", "90210",
				"NY", "New York", "10001",
				baseTime.Add(6*time.Hour),
			)

			// Test findQuoteRequestsMatchingLoad with useAppliedFields=false (suggested values)
			matchingQRs, err := findQuoteRequestsMatchingLoad(ctx, load, false)
			require.NoError(t, err)

			// Should find the VAN quote request
			assert.Len(t, matchingQRs, 1)
			assert.Equal(t, vanQR.ID, matchingQRs[0].ID)

			// Verify the correct quote requests were returned
			foundIDs := make(map[uint]bool)
			for _, qr := range matchingQRs {
				foundIDs[qr.ID] = true
			}

			assert.True(t, foundIDs[vanQR.ID], "Should find VAN quote request")

			// Verify all returned quote requests have the correct transport type
			for _, qr := range matchingQRs {
				assert.Equal(t, models.VanTransportType, qr.SuggestedRequest.TransportType)
			}
		})
	})
}
