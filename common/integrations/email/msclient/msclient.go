package msclient

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/microcosm-cc/bluemonday"
	"go.uber.org/zap"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/microsoft"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

var defaultScopes = []string{"https://graph.microsoft.com/.default"}

type (
	// Client is the set of exposed Outlook operations used by Drumkit services
	Client interface {
		WatchInbox(ctx context.Context, webhookURL, clientState string) (Subscription, error)
		RewatchInbox(ctx context.Context, webhookURL string, user models.UserAccessor) (Subscription, error)
		GetMessageByID(ctx context.Context, id string, opts ...Option) (msg Message, err error)
		StopWatchingInbox(ctx context.Context, subID string) error
		ListMessagesAfterDate(
			ctx context.Context,
			date time.Time,
		) (msgs []Message, err error)
		ListMessagesBetweenDates(
			ctx context.Context,
			startDate time.Time,
			endDate time.Time,
		) (msgs []Message, err error)
		SendMessage(ctx context.Context, msg *Message) error
		GetMessageAttachmentsByID(ctx context.Context, id string) ([]Attachment, error)
		AddAttachment(ctx context.Context, messageID string, attachment *Attachment) error
		GetDistributionLists(ctx context.Context) ([]GroupValue, error)
		GetDistributionListMembers(ctx context.Context, groupID string) ([]MemberValue, error)
		GetFolderByID(ctx context.Context, folderID string) (MailFolder, error)

		// Appends user's signature to the message body if it exists in models.User
		DraftMessage(ctx context.Context, msg *Message) error
		DraftReplyAll(ctx context.Context, msgID string, content string) (*Message, error)
		ReplyAll(ctx context.Context, msgID string, content string) (*Message, error)
		GetAuthenticatedUser() models.UserAccessor
	}

	// Service is a concrete Outlook implementation of the Client interface
	Service struct {
		// Owner's account email address
		user   models.UserAccessor
		client *http.Client

		// HACK: Only used for response logging; email creds are stored in user table
		integration models.Integration
	}

	Attachment struct {
		ODataType    string `json:"@odata.type"`
		ID           string `json:"id"`
		Name         string `json:"name"`
		ContentType  string `json:"contentType"`
		Size         int    `json:"size"`
		IsInline     bool   `json:"isInline"`
		ContentBytes string `json:"contentBytes,omitempty"`
	}

	AttachmentResponse struct {
		Value []Attachment `json:"value"`
	}

	// GroupValue represents a single group entry in the Graph API response
	GroupValue struct {
		ID                string   `json:"id"`
		DisplayName       string   `json:"displayName"`
		Mail              string   `json:"mail"`
		MailEnabled       bool     `json:"mailEnabled"`
		SecurityEnabled   bool     `json:"securityEnabled"`
		GroupTypes        []string `json:"groupTypes"`
		MailNickname      string   `json:"mailNickname"`
		ProxyAddresses    []string `json:"proxyAddresses"`
		VisibilityOptions string   `json:"visibility"`
	}

	// DistributionListResponse represents the Graph API response for distribution lists
	DistributionListResponse struct {
		OdataContext  string       `json:"@odata.context"`
		Value         []GroupValue `json:"value"`
		OdataNextLink string       `json:"@odata.nextLink,omitempty"`
	}

	// MemberValue represents a single member in a distribution list
	MemberValue struct {
		ID                string `json:"id"`
		DisplayName       string `json:"displayName"`
		Mail              string `json:"mail"`
		UserPrincipalName string `json:"userPrincipalName"`
	}

	// GroupMembersResponse represents the Graph API response for group members
	GroupMembersResponse struct {
		OdataContext  string        `json:"@odata.context"`
		Value         []MemberValue `json:"value"`
		OdataNextLink string        `json:"@odata.nextLink,omitempty"`
	}
)

func New[T models.UserAccessor](
	ctx context.Context,
	clientID,
	clientSecret string,
	user T,
	opts ...oauth.Option,
) (Client, error) {

	options := &oauth.Options{
		EncryptionKey: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	var oauthConfig = oauth2.Config{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		Endpoint:     microsoft.AzureADEndpoint(user.GetTenantID()),
	}
	oauthConfig.Endpoint.AuthStyle = oauth2.AuthStyleInParams

	ts, err := oauth.NewCachingTokenSource(ctx, user, &oauthConfig, oauth.WithEncryptionKey(options.EncryptionKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create caching token source: %w", err)
	}

	return &Service{
		user:   user,
		client: oauth.NewTracingClient(ctx, ts),
		integration: models.Integration{
			Model: gorm.Model{ID: user.GetID()},
			Type:  models.EmailType,
			Name:  models.Outlook,
		},
	}, nil
}

// Get on-behalf-of access and refresh token for user
func GetOboToken(
	ctx context.Context,
	auth MicrosoftAuthCodeRequest,
	clientID, clientSecret string,
) (token OBOToken, err error) {

	integration := models.Integration{Type: models.EmailType, Name: models.Outlook}

	// https://learn.microsoft.com/en-us/graph/api/subscription-post-subscriptions?view=graph-rest-1.0&tabs=http#example
	params := url.Values{}
	params.Set("grant_type", "urn:ietf:params:oauth:grant-type:jwt-bearer")
	params.Set("client_id", clientID)
	params.Set("client_secret", clientSecret)
	params.Set("assertion", auth.AccessToken)
	params.Set("scope", defaultScopes[0])
	params.Set("requested_token_use", "on_behalf_of")

	req, err := http.NewRequestWithContext(
		ctx, http.MethodPost,
		fmt.Sprintf("https://login.microsoftonline.com/%s/oauth2/v2.0/token", url.PathEscape(auth.Account.TenantID)),
		strings.NewReader(params.Encode()))

	if err != nil {
		return token, fmt.Errorf("error building POST token request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, integration, err)
		return token, fmt.Errorf("error sending POST token request: %w", err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, integration, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return token, fmt.Errorf("error reading body: %w", err)
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return token, errtypes.NewHTTPResponseError(integration, req, resp, body)
	}

	if err = json.Unmarshal(body, &token); err != nil {
		return token, fmt.Errorf("error unmarshaling token resp: %w", err)
	}

	return
}

// Creates an outlook subscription for new messages
func (s *Service) WatchInbox(ctx context.Context, webhookURL, clientState string) (sub Subscription, err error) {
	// Tells Microsoft Graph to send immutable IDs in change notifications
	// https://learn.microsoft.com/en-us/graph/outlook-immutable-id#immutable-id-with-change-notifications
	headers := http.Header{"Prefer": []string{`IdType="ImmutableId"`}}

	params := Subscription{
		// "me" alias: https://learn.microsoft.com/en-us/graph/call-api, https://shorturl.at/rFZ28
		// the graph api doesn't support filters in subscriptions
		Resource:           "me/messages",
		ChangeType:         "created",
		ClientState:        clientState,
		NotificationURL:    webhookURL,
		ExpirationDateTime: time.Now().Add(3 * 24 * time.Hour), // max time allowed
	}

	return sub, s.do(ctx, http.MethodPost, "subscriptions", params, &sub, headers)
}

// Subscriptions are 3 days long and must be renewed. Create new subscriptions if the resource is not found
// but the tokens are still valid.
// https://learn.microsoft.com/en-us/graph/api/subscription-update?view=graph-rest-1.0&tabs=http
// https://learn.microsoft.com/en-us/graph/change-notifications-overview#subscription-lifetime
func (s *Service) RewatchInbox(
	ctx context.Context,
	microsoftWebhookURL string,
	user models.UserAccessor,
) (sub Subscription, err error) {

	// Unsure if this is necessary in renewal but add to be safe
	headers := http.Header{"Prefer": []string{`IdType="ImmutableId"`}}
	params := map[string]any{"expirationDateTime": time.Now().Add(3 * 24 * time.Hour).Format(time.RFC3339)}

	err = s.do(ctx, http.MethodPatch,
		fmt.Sprintf("subscriptions/%s", url.PathEscape(user.GetOutlookSubscriptionID())),
		params, &sub, headers)

	if err != nil && err.Error() == "resource not found" {
		sub, err = s.WatchInbox(ctx, microsoftWebhookURL, user.GetOutlookClientState())
	}

	return sub, err
}

func (s *Service) StopWatchingInbox(ctx context.Context, subID string) error {
	return s.do(ctx, http.MethodDelete, fmt.Sprintf("subscriptions/%s", url.PathEscape(subID)), nil, nil, nil)
}

func (s *Service) GetMessageByID(ctx context.Context, id string, opts ...Option) (msg Message, err error) {
	options := &Options{
		ContentType:         PlaintextContentType,
		PreventInfiniteLoop: false,
	}

	for _, opt := range opts {
		opt(options)
	}

	// Set the Prefer header with ImmutableId
	headers := http.Header{"Prefer": []string{`IdType="ImmutableId"`}}

	// `ImmutableId` ensures that notifications and messages include the immutable ID.
	// https://learn.microsoft.com/en-us/graph/outlook-immutable-id

	if options.ContentType == PlaintextContentType {
		// `outlook.body-content-type="text"` ensures the content-type is returned as text instead of html
		// A Preference-Applied header is returned as confirmation if this Prefer header is specified.
		// If the header is not specified, the body and uniqueBody properties are returned in HTML format.
		// https://learn.microsoft.com/en-us/graph/api/message-get?view=graph-rest-1.0&tabs=http#request-headers
		headers["Prefer"] = append(headers["Prefer"], `outlook.body-content-type="text"`)
	}

	err = s.do(ctx, http.MethodGet, fmt.Sprintf("me/messages/%s", url.PathEscape(id)), nil, &msg, headers)
	if err != nil {
		if !options.PreventInfiniteLoop {
			// edge case: message doesn't have html so we error on fetch. Need to get the plaintext instead.
			return s.GetMessageByID(ctx, id, WithContentType(PlaintextContentType), WithPreventInfiniteLoop(true))
		}
		return msg, err
	}

	switch options.ContentType {
	case PlaintextContentType:
		return msg, nil
	case HTMLContentType:
		// Remove meta tags, the majority of tag attributes, and scripts.
		// UGC policy: https://github.com/microcosm-cc/bluemonday/blob/main/policies.go#L54
		p := bluemonday.UGCPolicy()
		p.AllowElements("body") // this seems to aid in converting HTML to markdown, which happens later.
		cleanHTML := p.Sanitize(msg.Body.Content)
		msg.Body.Content = cleanHTML
		return msg, nil

	default:
		return msg, fmt.Errorf("unsupported content type: %s", options.ContentType)
	}
}

func (s *Service) SendMessage(ctx context.Context, msg *Message) error {
	return s.do(ctx, http.MethodPost, fmt.Sprintf("me/messages/%s/send", msg.ID), nil, nil, nil)
}

// Callers must draft a message first to get its ID, then can call SendMessage. If user's signature exists
// and is configured to be used on new messages, this function appends it to the message body.
func (s *Service) DraftMessage(ctx context.Context, msg *Message) (err error) {
	if user, ok := s.user.(*models.User); ok && user.UseSignatureOnRepliesForwards {
		msg.Body.Content = msg.Body.Content + "<br/><br/>" + s.user.GetEmailSignature()
	}

	headers := http.Header{"Prefer": []string{`IdType="ImmutableId"`}}

	return s.do(ctx, http.MethodPost, "me/messages", msg, msg, headers)
}

// ReplyAll creates a draft reply message then sends it.
// If user's signature exists and is configured to be used on replies, this function appends it to the message body.
// TODO: Support attachments
func (s *Service) ReplyAll(ctx context.Context, msgID string, content string) (*Message, error) {

	if user, ok := s.user.(*models.User); ok && user.UseSignatureOnRepliesForwards {
		content = content + "<br/><br/>" + user.EmailSignature
	}
	msg := &ReplyMessage{Comment: content}

	// Create the draft first to receive the resulting message ID
	err := s.do(ctx, http.MethodPost, fmt.Sprintf("me/messages/%s/createReplyAll", msgID), msg, msg, nil)
	if err != nil {
		return &msg.Message, fmt.Errorf("error creating draft reply message: %w", err)
	}

	err = s.do(ctx, http.MethodPost, fmt.Sprintf("me/messages/%s/replyAll", msgID), msg, nil, nil)
	if err != nil {
		return &msg.Message, fmt.Errorf("error sending reply message: %w", err)
	}

	return &msg.Message, nil
}

// DraftReplyAll creates a draft reply message object, preserving the inline thread like the Outlook app.
// If user's signature exists, this function appends it to the message body.
// TODO: Support attachments
func (s *Service) DraftReplyAll(ctx context.Context, msgID string, draftContent string) (*Message, error) {
	if user, ok := s.user.(*models.User); ok && user.UseSignatureOnRepliesForwards {
		draftContent = draftContent + "<br/><br/>" + user.EmailSignature
	}

	draftResponse := &ReplyMessage{Comment: draftContent}
	err := s.do(
		ctx,
		http.MethodPost,
		fmt.Sprintf("me/messages/%s/createReplyAll", msgID),
		draftResponse,
		draftResponse,
		nil,
	)
	if err != nil {
		return &draftResponse.Message, fmt.Errorf("error creating draft reply message: %w", err)
	}

	return &draftResponse.Message, nil
}

// ListMessagesAfterDate fetches messages after a certain date (inclusive), tries HTML first,
// if HTML not available for a particular message, refetches as text,
// and if HTML is available, sanitizes HTML content.
func (s *Service) ListMessagesAfterDate(ctx context.Context, startDate time.Time) (msgs []Message, err error) {
	return s.listMessages(ctx, startDate, nil)
}

// ListMessagesBetweenDates fetches messages between two dates (inclusive), tries HTML first,
// if HTML not available for a particular message, refetches as text,
// and if HTML is available, sanitizes HTML content.
func (s *Service) ListMessagesBetweenDates(
	ctx context.Context,
	startDate time.Time,
	endDate time.Time,
) (msgs []Message, err error) {
	return s.listMessages(ctx, startDate, &endDate)
}

func (s *Service) listMessages(
	ctx context.Context,
	startDate time.Time,
	endDate *time.Time,
) (msgs []Message, err error) {

	// Initial fetch: attempt to get HTML content
	headers := http.Header{"Prefer": []string{`IdType="ImmutableId"`, `outlook.body-content-type="html"`}}

	// OData queries: https://shorturl.at/sACL6
	params := url.Values{}

	filterParts := []string{
		fmt.Sprintf("ReceivedDateTime ge %s", startDate.Format(time.RFC3339)),
	}
	if endDate != nil {
		filterParts = append(filterParts, fmt.Sprintf("ReceivedDateTime le %s", endDate.Format(time.RFC3339)))
	}

	params.Set("$filter", strings.Join(filterParts, " and "))
	params.Set("$top", "100")    // set page size
	params.Set("$count", "true") // the @odata.count property will be present only in the first page
	params.Set("$orderby", "ReceivedDateTime desc")

	var page int
	path := "me/messages/?" + params.Encode()

	for {
		page++
		var resp MessageListResponse
		if err = s.do(ctx, http.MethodGet, path, nil, &resp, headers); err != nil {
			log.ErrorNoSentry(ctx, fmt.Sprintf("error getting page %d", page), zap.Error(err))
			break
		}

		if page == 1 {
			log.Debug(ctx, "total message count", zap.Int("count", resp.DataCount))
		}

		// Process each message: If HTML is missing or isn't HTML, refetch as text.
		for i, m := range resp.Value {
			if strings.ToLower(m.Body.ContentType) != "html" {
				// Refetch this single message as text
				fallbackMsg, fErr := s.GetMessageByID(
					ctx,
					m.ID,
					WithContentType(PlaintextContentType),
					WithPreventInfiniteLoop(true),
				)
				if fErr != nil {
					log.ErrorNoSentry(
						ctx,
						"error refetching message in text mode",
						zap.String("messageID", m.ID),
						zap.Error(fErr),
					)
					// skip this message
					continue
				}

				m = fallbackMsg
			} else {
				// If we got HTML, sanitize it
				p := bluemonday.UGCPolicy()
				p.AllowElements("body")
				cleanHTML := p.Sanitize(m.Body.Content)
				m.Body.Content = cleanHTML
			}

			resp.Value[i] = m
		}

		msgs = append(msgs, resp.Value...)

		if resp.DataNextLink == "" {
			break
		}
		path = "me/" + strings.Split(resp.DataNextLink, "me/")[1]
	}

	return msgs, err
}

// Helper to execute HTTP requests using Microsoft-authenticated client.
// `dst` should be a pointer to a response struct.
// Function automatically adds Content-Type and Accept headers where appropriate; additional headers can be specified
// in `headers` arg (primary purpose is for Prefer)
func (s *Service) do(
	ctx context.Context,
	method string, path string,
	body any, dst any,
	headers http.Header,
) (err error) {

	log.With(ctx, zap.String("method", method), zap.String("path", path))

	var reqBody []byte
	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return fmt.Errorf("error marshaling request body: %w", err)
		}
	}

	req, err := http.NewRequestWithContext(
		ctx, method,
		fmt.Sprintf("https://graph.microsoft.com/v1.0/%s", path),
		bytes.NewReader(reqBody))
	if err != nil {
		return fmt.Errorf("error building request: %w", err)
	}

	// Add headers
	for k, vs := range headers {
		for _, v := range vs {
			req.Header.Add(k, v)
		}
	}

	req.Header.Set("Accept", "application/json")

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	log.Debug(ctx, "request headers", zap.Any("headers", req.Header))

	resp, err := s.client.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, s.integration, err)
		return fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, s.integration, resp.StatusCode)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	if resp.StatusCode == http.StatusNotFound {
		return errors.New("resource not found")
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return errtypes.NewHTTPResponseError(s.integration, req, resp, respBody)
	}

	if dst != nil {
		if err = json.Unmarshal(respBody, dst); err != nil {
			return fmt.Errorf("error unmarshaling response body into dst: %w", err)
		}
	}

	return nil
}

// GetMessageAttachmentsByID fetches attachments for a message with a proper retry mechanism
// that respects context cancellation and uses backoff between retries.
func (s *Service) GetMessageAttachmentsByID(ctx context.Context, id string) ([]Attachment, error) {
	headers := http.Header{"Prefer": []string{`IdType="ImmutableId"`}}

	var resp AttachmentResponse
	var err error

	maxRetries := 1
	initialBackoff := 200 * time.Millisecond
	maxBackoff := 1 * time.Second

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if ctx.Err() != nil {
			return nil, fmt.Errorf("context canceled before attempt %d: %w", attempt, ctx.Err())
		}

		err = s.do(
			ctx,
			http.MethodGet,
			fmt.Sprintf("me/messages/%s/attachments", url.PathEscape(id)),
			nil,
			&resp,
			headers,
		)

		if err == nil {
			return resp.Value, nil
		}

		if attempt == maxRetries {
			break
		}

		// Log the failure and backoff
		log.WarnNoSentry(ctx, "failed to get attachments, retrying...",
			zap.Error(err),
			zap.Int("attempt", attempt+1),
			zap.Int("maxRetries", maxRetries))

		backoff := min(initialBackoff*time.Duration(1<<attempt), maxBackoff)

		// Add jitter (±20%)
		//nolint:gosec
		jitter := time.Duration(float64(backoff) * (0.8 + 0.4*rand.Float64()))

		timer := time.NewTimer(jitter)
		select {
		case <-timer.C:
			// Continue to next attempt
		case <-ctx.Done():
			if !timer.Stop() {
				<-timer.C // Drain the channel
			}
			return nil, fmt.Errorf("context canceled during backoff: %w", ctx.Err())
		}
	}

	return nil, fmt.Errorf("failed to get attachments after %d attempts: %w", maxRetries+1, err)
}

func (s *Service) GetAuthenticatedUser() models.UserAccessor {
	return s.user
}

func (s *Service) AddAttachment(ctx context.Context, messageID string, attachment *Attachment) error {
	return s.do(
		ctx,
		http.MethodPost,
		fmt.Sprintf("me/messages/%s/attachments", url.PathEscape(messageID)),
		attachment,
		nil,
		nil,
	)
}

// NOTE: 5/20/2025 - Distribution List related code is not used/called anywhere.
// - See Github PR https://github.com/drumkitai/drumkit/pull/1402 and Linear issue for more details.
// - Blocked prev by Microsoft Graph API Permissions Issues

// GetDistributionLists fetches distribution lists from the Microsoft Graph API
// Distribution lists are mail-enabled groups that are not Microsoft 365 groups
func (s *Service) GetDistributionLists(ctx context.Context) ([]GroupValue, error) {
	// Use the Graph API to get all groups, then filter in memory
	// Reference: https://learn.microsoft.com/en-us/graph/api/group-list
	// TODO: use query params instead of filtering groups
	//       querying ilter=mailEnabled eq true and securityEnabled eq false
	//       doesn't work returns "Unsupported Query" in response message
	endpoint := "groups"

	var allGroups []GroupValue
	currentURL := endpoint

	for currentURL != "" {
		var response DistributionListResponse

		// Set the Prefer header with ImmutableId
		headers := http.Header{"Prefer": []string{`IdType="ImmutableId"`}}

		if err := s.do(ctx, http.MethodGet, currentURL, nil, &response, headers); err != nil {
			return nil, fmt.Errorf("failed to fetch distribution lists: %w", err)
		}

		// Filter groups in memory to get only distribution lists
		// TODO: remove and use query params in initial api request to graph api
		for _, group := range response.Value {
			// Distribution lists are mail-enabled but not security-enabled
			if group.MailEnabled && !group.SecurityEnabled {
				allGroups = append(allGroups, group)
			}
		}

		// Check if we need to get more pages
		if response.OdataNextLink != "" {
			// Extract the relative path from the next link URL
			// The next link URL will be a full URL, but we need just the path after v1.0/
			parts := strings.Split(response.OdataNextLink, "v1.0/")
			if len(parts) != 2 {
				return nil, fmt.Errorf("invalid next link format: %s", response.OdataNextLink)
			}
			currentURL = parts[1]
		} else {
			currentURL = ""
		}
	}

	return allGroups, nil
}

func (s *Service) GetDistributionListMembers(ctx context.Context, groupID string) ([]MemberValue, error) {
	headers := http.Header{"Prefer": []string{`IdType="ImmutableId"`}}

	var resp GroupMembersResponse
	err := s.do(
		ctx,
		http.MethodGet,
		fmt.Sprintf("groups/%s/members", url.PathEscape(groupID)),
		nil,
		&resp,
		headers,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get distribution list members: %w", err)
	}

	return resp.Value, nil
}

// GetFolderByID retrieves a mail folder by its ID using the Microsoft Graph API
// https://learn.microsoft.com/en-us/graph/api/mailfolder-get?view=graph-rest-1.0&tabs=http
func (s *Service) GetFolderByID(ctx context.Context, folderID string) (folder MailFolder, err error) {
	// Set the Prefer header with ImmutableId for consistency with other methods
	headers := http.Header{"Prefer": []string{`IdType="ImmutableId"`}}

	err = s.do(
		ctx,
		http.MethodGet,
		fmt.Sprintf("me/mailFolders/%s", url.PathEscape(folderID)),
		nil,
		&folder,
		headers,
	)
	if err != nil {
		return folder, fmt.Errorf("failed to get folder by ID %s: %w", folderID, err)
	}

	return folder, nil
}
