package extractor

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	promptBuilder "github.com/drumkitai/drumkit/common/integrations/llm/prompt_builder"
	"github.com/drumkitai/drumkit/common/integrations/tms/mcleodenterprise"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsUserDB "github.com/drumkitai/drumkit/common/rds/tmsuser"
)

//nolint:lll
const (
	GenericInstructions = `
	Missing Information: If any required information is absent in the shipping order, set the field's value to empty unless otherwise specified on a per field basis.
	Data Integrity: Only extract information directly from the shipping order; do not make any assumptions or add any data not explicitly provided.
	IMPORTANT: Provide ONLY the JSON object in your response, nothing else. Do not include any explanatory text, markdown formatting, or code blocks around the JSON.
	Do not use the example output provided here in the final response. The example is for reference purposes only.
	`
)

//nolint:lll
var (
	dateTimeNotes = `
	Date/Time Formatting:
	If a specific date & time is provided, format it as "mm/dd/yyyy, hh:MM+AM/PM".
		Example: "03/07/2024, 02:00AM".
	Do not mix formats - if time is in 24-hour format, always convert to 12-hour time with AM/PM indicators.
	If no specific time is given, use only the date in the format "mm/dd/yyyy".
		Example: "03/07/2024".
	If year is not specified, infer based on today's date (` +
		time.Now().Local().Format(time.DateOnly) + `).
	Avoid partial or incorrect formats, such as "03/07/2024, " or "03/07". If the date is incomplete (lacking month, day, or year), set the field to null.
	`
)

// extractBasicInfo extracts basic load information from either an email body or attachment
func extractBasicInfo(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	attachment models.Attachment,
	tms models.Integration,
	customerID uint,
	initialResponseID string,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	historicalLoadData []models.LoadReferenceInfo,
) (result BasicInfo, rawResult BasicInfo, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractBasicInfo", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	var refNumNotes string

	// Handle special case for MJB to add specific instructions
	if email.ServiceID == TridentServiceID {
		if check := checkForStringInUserPrompt("MJB", userPrompt); check {
			refNumNotes = `Note in the case of MJB, the ref # is the shipping order number listed after 'Ship To'.
            And the Customer PO should be assigned to the poNums field.`
		}
	}

	//nolint:lll
	prompt := fmt.Sprintf(`
    Extract the following basic information:

    Instructions:
    * "refNumber": Extract the shipping order number or the release number. Never extract a warehouse code as the refNumber.
        - If both a PO # and a order/release/shipping order # are provided, this field should be the order/release number, not the PO #.
        - Never include additional text here, only extract the ref number itself.
		%s
    * "poNums": Extract the PO # if provided. If both a PO # and a order/release/shipping order # are provided, this field should be the PO #.


    %s
    `, refNumNotes, GenericInstructions)

	// Conditionally add the historical data for pattern recognition
	if len(historicalLoadData) > 0 {
		// Use a formatted string or JSON to clearly present the examples
		historicalDataStr := ""
		for _, load := range historicalLoadData {
			if load.CustomerRefNumber != "" || load.PoNums != "" {
				historicalDataStr += fmt.Sprintf(" - refNumber: %s, poNums: %v\n", load.CustomerRefNumber, load.PoNums)
			}
		}

		//nolint:lll
		prompt += fmt.Sprintf(`

        Use the following historical data to help identify patterns in the refNumber and poNums.
        Analyze these patterns for:
        1. Length: How many characters are typically used
        2. Format: Whether it uses only numbers, mix of letters/numbers, or special characters
        3. Prefix patterns: Common starting characters or sequences
        4. Numeric patterns: Position and length of numeric sequences
        5. Delimiter usage: Common separators like '-', '_', or '/'

        Historical examples:
        %s

        Use these patterns to identify new reference numbers against the established format. You should NOT use this historical data in your response, they are only meant to serve as a reference.
        `, historicalDataStr)
	}

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForExtractor(
		ctx,
		tms.ID,
		customerID,
		"loadBuilding",
		"basicInfo",
		prompt,
	)
	if err != nil {
		log.Warn(ctx, "error building custom basicInfo prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	// Prepare reference number metadata for Braintrust
	extraMetadata := map[string]any{
		"historical_ref_numbers": historicalLoadData,
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			Schema:             GenerateSchema[BasicInfo](),
			DeveloperPrompt:    prompt,
			PreviousResponseID: initialResponseID,
			ExtraMetadata:      extraMetadata,
		},
	)
	if err != nil {
		return result, result, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[BasicInfo](response.Content)
	if err != nil {
		return result, result, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return result, result, response.BraintrustLogID, nil
}

// extractCustomer extracts customer information from either an email body or attachment
func extractCustomer(
	ctx context.Context,
	openaiService openai.Service,
	tmsID uint,
	email models.Email,
	attachment models.Attachment,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (
	tmsCustomer *models.TMSCustomer,
	rawLLMCustomer CustomerInfo,
	initialResponseID string,
	braintrustLogID string,
	err error,
) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractCustomer", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	accountDomain := extractDomain(email.Account)

	//nolint:lll
	prompt := fmt.Sprintf(`You are an expert logistics analyst. Your task is to identify the **shipper's company name** and the **original sender's email address** from the provided context.

**Context:**
You will be given a set of information organized with the following tags:
- <email_sender>: The email address of the party who sent the email to us.
- <email_subject>: The subject line of the email.
- <email_body>: The full text content of the email. This is often the most important section. Look for company names in signatures or greetings.
- <pdf_content>: The text content extracted from any PDF attachment. This is also a primary source for the shipper's name.

**Extraction Instructions:**
1.  **shipper_name**:
    - Your primary goal is to find the name of the company that is requesting the shipment (the shipper).
    - **Prioritize the <email_body>**. Look for company names in email signatures, greetings (e.g., "This is Jane from..."), or forwarded messages.
    - If the body is unhelpful, look for the shipper's name in the <pdf_content>, often near labels like "Shipper:", "From:", or at the top of the document.
    - The shipper is the company sending the goods, not a logistics provider or a warehouse, unless they are the same entity.
    - **Do NOT** include suffixes like "LLC", "INC", or "CO". Extract the clean company name.
    - If no company name can be found, leave the field empty.

2.  **original_sender_email**:
    - Find the email address of the person who originally sent the request.
    - Check the <email_body> for forwarded message blocks. Look for a 'From:' line that has a different domain than our company's domain, which is '%s'.
    - If there is no forwarded block, use the value from <email_sender>.

**Examples:**

### Example 1: Name in Email Body
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>FW: New Load</email_subject>
<email_body>
Hi team,
Please see attached from our customer.
Thanks,
--
From: "Jane Doe" <<EMAIL>>
Date: Tuesday, July 30, 2024 at 11:00 AM
Subject: New Load

Please quote the attached.
</email_body>
<pdf_content>
SHIPPER: LMNOP Goods
123 Main St...
</pdf_content>

**Output:**
{
  "shipper_name": "LMNOP Goods",
  "original_sender_email": "<EMAIL>"
}

### Example 2: Name in PDF Only
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>Load for you</email_subject>
<email_body>
See attached for the new load.
</email_body>
<pdf_content>
Load Confirmation
Shipper: Alpha Logistics, LLC
Pickup: Warehouse B
...
</pdf_content>

**Output:**
{
  "shipper_name": "Alpha Logistics",
  "original_sender_email": "<EMAIL>"
}

### Example 3: Forwarded Email Chain
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>Fwd: Load Tender 123</email_subject>
<email_body>
---------- Forwarded message ---------
From: Bob Johnson <<EMAIL>>
Date: Mon, Jul 29, 2024 at 10:00 AM
Subject: Load Tender 123
To: <EMAIL>

Hi, please see the attached file for details on the new load.
</email_body>
<pdf_content>
...
</pdf_content>

**Output:**
{
  "shipper_name": "Shipper Company",
  "original_sender_email": "<EMAIL>"
}

%s
        `, accountDomain, GenericInstructions)

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForTMS(
		ctx,
		tmsID,
		"loadBuilding",
		models.PromptExtractorNameCustomer,
		prompt,
	)
	if err != nil {
		log.Warn(ctx, "error building custom customer prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt: prompt,
			Schema:          GenerateSchema[CustomerInfo](),
			UserPrompt:      userPrompt,
			Temperature:     openai.TemperatureDeterministic,
		},
	)
	if err != nil {
		return tmsCustomer,
			rawLLMCustomer,
			response.ResponseID,
			response.BraintrustLogID,
			fmt.Errorf("error getting response: %w", err)
	}

	result, err := StructExtractor[CustomerInfo](response.Content)
	if err != nil {
		return tmsCustomer,
			rawLLMCustomer,
			response.ResponseID,
			response.BraintrustLogID,
			fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	rawLLMCustomer = result

	// Try looking up by name parsed from LLM
	if result.ShipperName != "" && !strings.Contains(strings.ToLower(result.ShipperName), "lmnop") {
		// If the shipper name is not LMNOP, then we need to find the original sender email
		// and use that to map to a TMSCustomer
		mappedCustomer, err := MapCustomer(
			ctx, tmsID, result.ShipperName, userPrompt,
		)
		if err != nil {
			return tmsCustomer,
				rawLLMCustomer,
				response.ResponseID,
				response.BraintrustLogID,
				fmt.Errorf("failed to map customer %s to customer: %w", result.ShipperName, err)
		} else if mappedCustomer != nil {
			return mappedCustomer, rawLLMCustomer, response.ResponseID, response.BraintrustLogID, nil
		}
	}

	// If no mapped customer, then try looking up by original sender email
	if result.OriginalSenderEmail == "" || strings.Contains(strings.ToLower(result.OriginalSenderEmail), "lmnop") {
		log.WarnNoSentry(
			ctx, "no original sender email found",
			zap.String("originalSenderEmail", result.OriginalSenderEmail),
		)

		return tmsCustomer, rawLLMCustomer, response.ResponseID, response.BraintrustLogID, nil
	}

	domain := ExtractSecondLevelDomain(result.OriginalSenderEmail)
	if domain == "" {
		domain = result.OriginalSenderEmail
	}

	mappedCustomer, err := MapCustomer(
		ctx, tmsID, domain, userPrompt,
	)
	if err != nil {
		return tmsCustomer,
			rawLLMCustomer,
			response.ResponseID,
			response.BraintrustLogID,
			fmt.Errorf("failed to map domain %s to customer: %w", domain, err)
	}

	return mappedCustomer, rawLLMCustomer, response.ResponseID, response.BraintrustLogID, nil
}

// extractRateData extracts rate information from either an email body or attachment
func extractRateData(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	attachment models.Attachment,
	tms models.Integration,
	customerID uint,
	previousResponseID string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (result RateDataInfo, rawResult RateDataInfo, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractRateData", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	//nolint:lll
	prompt := fmt.Sprintf(`
    Extract the following rate information:
    Instructions:

		Field Mapping:
		"collectionMethod": Must be one of the following values: "COD", "Collect", "Third-Party", or "Prepaid". If the collection method is not explicitly stated in the shipping order, default to "Prepaid".
		"customerRateType": Must be one of the following values: "Flat", "Distance", "CWT" (hundredweight), or "Tons". If the rate type is not explicitly stated, default to "Flat".
		"customerLineHaulRate": Set this field to the specified customer rate if explicitly mentioned. If not, set it to null.
		"customerRateNumUnits": Number of units for customer rate calculation (e.g., miles for distance-based rates). If rate type is flat, set to 1.

		The load data may refer to "carrier pay" or "carrier rates" but this is in fact the customer rates from our perspective.

		%s
    `, GenericInstructions)

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForExtractor(
		ctx,
		tms.ID,
		customerID,
		"loadBuilding",
		"rateData",
		prompt,
	)

	if err != nil {
		log.Warn(ctx, "error building custom rateData prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[RateDataSchema](),
			PreviousResponseID: previousResponseID,
			// TODO: Enable temperature for all extraction prompts when we have the time to test it.
			// Temperature:     openai.TemperatureDeterministic,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[RateDataInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	if strings.EqualFold(result.RateData.CustomerRateType, "flat") {
		result.RateData.CustomerLineHaulCharge = models.ValueUnit{
			Val:  result.RateData.CustomerLineHaulRate,
			Unit: helpers.Or(result.RateData.CustomerLineHaulUnit, "USD"),
		}

		result.RateData.CustomerRateNumUnits = 1
	}

	if tms.Name != models.McleodEnterprise {
		// Skip Mcleod specific rate data processing
		return result, rawResult, response.BraintrustLogID, nil
	}

	if !helpers.IsStringInArray(
		[]string{"COD", "Collect", "Third-Party", "Prepaid"},
		result.RateData.CollectionMethod,
	) {
		// HACK: Historical data shows most of Payton's customers are using Third-Party
		if strings.Contains(email.Account, "<EMAIL>") ||
			strings.Contains(email.Account, "<EMAIL>") {

			result.RateData.CollectionMethod = "Third-Party"
		} else {
			result.RateData.CollectionMethod = "Prepaid"
		}
	}

	// HACK: Hard-code revenue code for Payton because his email is different in Mcleod
	if strings.Contains(email.Account, "<EMAIL>") ||
		strings.Contains(email.Account, "<EMAIL>") {

		result.RateData.RevenueCode = "Charleston"

	} else {
		tmsUser, err := tmsUserDB.GetByEmail(ctx, email.Account, tms.ID)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"error getting TMS user by email for revenue code",
				zap.Error(err),
				zap.String("emailAddress", email.Account),
			)
		} else {
			result.RateData.RevenueCode = tmsUser.RevenueCode
		}
	}

	if !helpers.IsStringInArray([]string{"Flat", "Distance", "CWT", "Tons"}, result.RateData.CustomerRateType) {
		result.RateData.CustomerRateType = "Flat"
	}

	return result, rawResult, response.BraintrustLogID, nil
}

// QN is hh:MMam/pm a typo? There should be a space between the time and the AM/PM?
// extractPickup extracts pickup information from either an email body or attachment
func extractPickup(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	attachment models.Attachment,
	tms models.Integration,
	customerID uint,
	previousResponseID string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	historicalLoadData []models.LoadReferenceInfo,
	options Options,
) (result PickupInfo, rawResult PickupInfo, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractPickup", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	var tmsSpecificInstructions string

	if tms.Name == models.Turvo {
		tmsSpecificInstructions = getTurvoAppointmentInstructions("pickup/origin")
	}

	//nolint:lll
	prompt := fmt.Sprintf(`You are an expert logistics data extractor. Based on the shipping information from our previous conversation, extract pickup location details and format them into a single, valid JSON object.

### **Output Format**
Your response MUST be a single JSON object that strictly follows this structure:

{
    "pickup": {
        "name": "Company Name",
        "addressLine1": "Street Address",
        "addressLine2": "Suite/Unit",
        "city": "City",
        "state": "ST",
        "zipCode": "12345",
        "country": "US",
        "contact": "Contact Name",
        "phone": "************",
        "email": "<EMAIL>",
        "businessHours": "Business Hours",
        "refNumber": "Reference Number",
        "readyTime": "mm/dd/yyyy, hh:MMam/pm",
        "apptType": "By appointment",
        "apptStartTime": "mm/dd/yyyy, hh:MMam/pm",
        "apptEndTime": "mm/dd/yyyy, hh:MMam/pm",
        "apptNote": "Appointment notes",
        "timezone": "EDT"
    }
}

### **Extraction Rules**
Apply these rules when extracting pickup data:

* **Pickup Location Identification**: Look for pickup information such as loading locations, ship from addresses, origin points, or shipper details.
    * Look for sections labeled "Ship From:", "Pickup From:", "Origin:", "Shipper:", "Loading Location:", or "Load From:"
    * **CRITICAL**: Do NOT extract "Ship To:" or destination information - only extract pickup/origin details
    * If multiple locations are present, extract only the pickup/origin location

* **Field Requirements**:
    * **name**: Company or facility name (exclude address from this field)
    * **addressLine1**: Street address  
    * **addressLine2**: Suite, unit, etc. (leave empty if not specified)
    * **city**: City name
    * **state**: State code (e.g., "GA", "TX")  
    * **zipCode**: Postal code
    * **country**: Country code (default to "US")
    * **contact**: Contact person name. If multiple contacts are specified, extract the first one. Note the contact is **never** from %s company or has email domains %s.
    * **phone**: Phone number (format: ************). If multiple phone numbers are specified, extract the first one.
    * **email**: Email address. If multiple email addresses are specified, extract the first one.
    * **businessHours**: Business hours if specified
    * **refNumber**: Reference number specific to the pickup location. 
        - Look for numbers labeled as "Pickup Number", "Order Number", "Shipper Reference", or similar pickup-specific identifiers
        - AVOID numbers labeled as "Load ID", "BOL", "PRO", or "PO Number" - these belong at other levels
        - Pay attention to document structure: numbers appearing in pickup sections, near pickup addresses, or under pickup headings are more likely to be pickup-specific
        - When multiple reference numbers are present, prioritize:
          1) Numbers explicitly labeled as pickup-specific
          2) Numbers that appear only for this pickup stop (not repeated across all stops)
          3) Numbers that are clearly pickup-specific rather than shipment-wide
    * **readyTime**: ONLY set when explicitly stated as "ready time", "ready date", "available from", etc. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from appointment times.
    * **apptType**: "By appointment", "FCFS", or appointment type
    * **apptStartTime**: ONLY set when there is an explicit appointment or scheduled window start time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from ready times.
    * **apptEndTime**: ONLY set when there is an explicit appointment or scheduled window end time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from ready times.
    * **apptNote**: Notes about the appointment or pickup instructions
    * **timezone**: Timezone code (e.g., "EDT", "CST"). Look for timezone indicators near pickup times.

%s

%s

### **IMPORTANT: Reference Number Clarification**
Documents often contain many reference numbers. For pickup refNumber fields:
- EXTRACT: Numbers labeled as "Pickup Number", "Order Number", "Shipper Reference", or similar pickup-specific identifiers
- IGNORE: Numbers labeled as "Load ID", "BOL", "PRO" (these are shipment-level)
- IGNORE: Numbers labeled as "PO Number" (this goes in the customer poNums field)

When in doubt, choose the number that is most specific to this particular pickup stop rather than the overall shipment.

**Examples:**

### Example 1: Standard Pickup with Appointment
**Input:**
A shipping document contains:
SHIP FROM:
Mohawk Industries
1405 HWY 41 S
CALHOUN, GA 30701
Contact: Sarah Johnson
Phone: ************
Email: <EMAIL>
Ready Time: 08/26/2025, 8:00AM
Appointment Required: 9:00AM-10:00AM EDT
Special Instructions: Driver must check in at front desk

**Output:**
{
    "pickup": {
        "name": "Mohawk Industries",
        "addressLine1": "1405 HWY 41 S",
        "addressLine2": "",
        "city": "CALHOUN",
        "state": "GA",
        "zipCode": "30701",
        "country": "US",
        "contact": "Sarah Johnson",
        "phone": "************",
        "email": "<EMAIL>",
        "businessHours": "",
        "refNumber": "",
        "readyTime": "08/26/2025, 08:00AM",
        "apptType": "By appointment",
        "apptStartTime": "08/26/2025, 09:00AM",
        "apptEndTime": "08/26/2025, 10:00AM",
        "apptNote": "Driver must check in at front desk",
        "timezone": "EDT"
    }
}

### Example 2: Multiple Reference Numbers
**Input:**
Stop 1 (pickup)
Mobil, 1001 Billingsport Rd., Paulsboro, NJ 08066
Leah Scalise Phone: (*************
Pickup: 09/12/2025 07:00AM - 09/12/2025 03:00PM

- SN834918 (BOL)
- 2608089680 (Delivery/Order Number)  
- 4700829 (PO Number)
- LD385193 (Load ID)
- LD385193 (PRO)

**Output:**
{
    "pickup": {
        "name": "Mobil",
        "addressLine1": "1001 Billingsport Rd.",
        "addressLine2": "",
        "city": "Paulsboro",
        "state": "NJ",
        "zipCode": "08066",
        "country": "US",
        "contact": "Leah Scalise",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "2608089680",
        "readyTime": "09/12/2025, 07:00AM",
        "apptType": "By appointment",
        "apptStartTime": "09/12/2025, 07:00AM",
        "apptEndTime": "09/12/2025, 03:00PM",
        "apptNote": "",
        "timezone": ""
    }
}

### Example 3: Multiple Locations - Extract Origin Only
**Input:**
SHIP FROM:
Origin Warehouse LLC
500 Commerce Dr
Atlanta, GA 30309
Ready: 08/28/2025, 2:00PM

SHIP TO:
Destination Corp
789 Delivery St
Miami, FL 33101
Deliver by: 08/30/2025

**Output:**
{
    "pickup": {
        "name": "Origin Warehouse LLC",
        "addressLine1": "500 Commerce Dr",
        "addressLine2": "",
        "city": "Atlanta",
        "state": "GA",
        "zipCode": "30309",
        "country": "US",
        "contact": "",
        "phone": "",
        "email": "",
        "businessHours": "",
        "refNumber": "",
        "readyTime": "08/28/2025, 02:00PM",
        "apptType": "",
        "apptStartTime": "08/28/2025, 02:00PM",
        "apptEndTime": "",
        "apptNote": "",
        "timezone": ""
    }
}

%s
    `, options.Service.Name, options.Service.EmailDomains, tmsSpecificInstructions, dateTimeNotes, GenericInstructions)

	// Append the historical data to the prompt if it exists.
	if len(historicalLoadData) > 0 {
		historicalDataStr := ""
		for _, load := range historicalLoadData {
			if load.PickupRefNumber != "" {
				historicalDataStr += fmt.Sprintf(" - refNumber: %s\n", load.PickupRefNumber)
			}
		}

		//nolint:lll
		prompt += fmt.Sprintf(`
### **Historical Data for Pattern Recognition**
The following are examples of real, confirmed pickup reference numbers. Analyze these patterns for:
1. Length: How many characters are typically used
2. Format: Whether it uses only numbers, mix of letters/numbers, or special characters
3. Prefix patterns: Common starting characters or sequences (e.g., "PU-", "PICK-")
4. Numeric patterns: Position and length of numeric sequences
5. Delimiter usage: Common separators like '-', '_', or '/'

Historical examples:
%s

Use these patterns to validate new pickup reference numbers against the established format. You should NOT use this historical data in your response, they are only meant to serve as a reference.
`, historicalDataStr)
	}

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForExtractor(
		ctx,
		tms.ID,
		customerID,
		"loadBuilding",
		"pickup",
		prompt,
	)

	if err != nil {
		log.Warn(ctx, "error building custom pickup prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	// Prepare reference number metadata for Braintrust
	extraMetadata := map[string]any{
		"historical_pickup_ref_numbers": historicalLoadData,
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[PickupSchema](),
			PreviousResponseID: previousResponseID,
			Temperature:        openai.TemperatureDeterministic,
			ExtraMetadata:      extraMetadata,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[PickupInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	if tms.Name == models.Aljex {
		result.Pickup.Phone, err = FormatPhoneCustom(result.Pickup.Phone)
		if err != nil {
			log.WarnNoSentry(ctx, "error formatting phone number", zap.Error(err))
		}
	} else {

		// McLeodEnterprise: Use readyTime as fallback for appointment times if they're not set
		if tms.Name == models.McleodEnterprise {
			if !result.Pickup.ApptStartTime.Valid && result.Pickup.ReadyTime.Valid {
				result.Pickup.ApptStartTime = result.Pickup.ReadyTime
			}
		}

		tz, err := timezone.GetTimezoneByZipOrCity(
			ctx, result.Pickup.Zipcode, result.Pickup.City, result.Pickup.State, "",
		)
		if err != nil {
			log.WarnNoSentry(ctx, "error getting pickup timezone", zap.Error(err))
		} else {
			// Start time and end time are in UTC, so we need to denormalize them to the pickup's timezone
			denormalizedTime, err := timezone.DenormalizeUTC(
				result.Pickup.ApptStartTime.Time, tz)
			if err == nil {
				result.Pickup.ApptStartTime.Time = denormalizedTime
			}

			denormalizedTime, err = timezone.DenormalizeUTC(result.Pickup.ApptEndTime.Time, tz)
			if err == nil {
				result.Pickup.ApptEndTime.Time = denormalizedTime
			}

			denormalizedTime, err = timezone.DenormalizeUTC(result.Pickup.ReadyTime.Time, tz)
			if err == nil {
				result.Pickup.ReadyTime.Time = denormalizedTime
			}
		}
	}

	result.Pickup.CompanyCoreInfo = ValidateStop(ctx, "pickup", result.Pickup.CompanyCoreInfo, tms.ID)

	return result, rawResult, response.BraintrustLogID, nil
}

// extractConsignee extracts consignee information from either an email body or attachment
func extractConsignee(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	attachment models.Attachment,
	tms models.Integration,
	customerID uint,
	previousResponseID string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	historicalLoadData []models.LoadReferenceInfo,
	options Options,
) (result ConsigneeInfo, rawResult ConsigneeInfo, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractConsignee", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	var tmsSpecificInstructions string

	if tms.Name == models.Turvo {
		tmsSpecificInstructions = getTurvoAppointmentInstructions("delivery")
	}

	//nolint:lll
	prompt := fmt.Sprintf(`You are an expert logistics data extractor. Based on the shipping information from our previous conversation, extract consignee (delivery destination) details and format them into a single, valid JSON object.

### **Output Format**
Your response MUST be a single JSON object that strictly follows this structure:

{
	"consignee": {
		"name": "Company Name",
		"addressLine1": "Street Address",
		"addressLine2": "Suite/Unit",
		"city": "City",
		"state": "ST",
		"zipCode": "12345",
		"country": "US",
		"contact": "Contact Name",
		"phone": "************",
		"email": "<EMAIL>",
		"businessHours": "Business Hours",
		"refNumber": "Reference Number",
		"mustDeliver": "mm/dd/yyyy, hh:MMam/pm",
		"apptType": "By appointment",
		"apptStartTime": "mm/dd/yyyy, hh:MMam/pm",
		"apptEndTime": "mm/dd/yyyy, hh:MMam/pm",
		"apptNote": "Appointment notes",
		"timezone": "EDT",
		"externalTMSID": null
	}
}

### **Extraction Rules**
Apply these rules when extracting consignee data:

* **Consignee Location Identification**: Look for delivery information such as destination addresses, deliver to locations, or consignee details.
    * Look for sections labeled "Ship To:", "Deliver To:", "Consignee:", "Destination:", or "Delivery Location:"
    * **CRITICAL**: Do NOT extract "Ship From:" or origin information - only extract delivery/destination details
    * The consignee is the FINAL DELIVERY LOCATION for the shipment
    * If there are multiple stops, extract the LAST stop as the consignee

* **Field Requirements**:
    * **name**: Company or facility name (exclude address from this field)
    * **addressLine1**: Street address
    * **addressLine2**: Suite, unit, etc. (leave empty if not specified)
    * **city**: City name
    * **state**: State code (e.g., "IL", "TX")
    * **zipCode**: Postal code
    * **country**: Country code (default to "US")
    * **contact**: Contact person name. If multiple contacts are specified, extract the first one. Note the contact is **never** from %s company or has email domains %s.
    * **phone**: Phone number (format: ************). If multiple phone numbers are specified, extract the first one.
    * **email**: Email address
    * **businessHours**: Business hours if specified
    * **refNumber**: Reference number specific to the delivery location.
        - Look for numbers labeled as "Delivery Number", "Consignee Order", "Receiver Reference", or similar delivery-specific identifiers
        - AVOID numbers labeled as "Load ID", "BOL", "PRO", or "PO Number" - these belong at other levels
        - Pay attention to document structure: numbers appearing in delivery sections, near consignee addresses, or under delivery headings are more likely to be delivery-specific
        - When multiple reference numbers are present, prioritize:
          1) Numbers explicitly labeled as delivery-specific
          2) Numbers that appear only for this delivery stop (not repeated across all stops)
          3) Numbers that are clearly delivery-specific rather than shipment-wide
    * **mustDeliver**: ONLY set when explicitly stated as "must deliver by", "delivery deadline", "deliver no later than", etc. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from appointment times.
    * **apptType**: "By appointment", "FCFS", or appointment type
    * **apptStartTime**: ONLY set when there is an explicit delivery appointment or scheduled window start time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from must-deliver times.
    * **apptEndTime**: ONLY set when there is an explicit delivery appointment or scheduled window end time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from must-deliver times.
    * **apptNote**: Notes about the appointment or delivery instructions
    * **timezone**: Timezone code (e.g., "EDT", "CST"). Look for timezone indicators near delivery times.
    * **externalTMSID**: Set to null

%s

%s

### **IMPORTANT: Reference Number Clarification**
Documents often contain many reference numbers. For consignee refNumber fields:
- EXTRACT: Numbers labeled as "Delivery Number", "Consignee Order", "Receiver Reference", or similar delivery-specific identifiers
- IGNORE: Numbers labeled as "Load ID", "BOL", "PRO" (these are shipment-level)
- IGNORE: Numbers labeled as "PO Number" (this goes in the customer poNums field)

When in doubt, choose the number that is most specific to this particular delivery stop rather than the overall shipment.

**Examples:**

### Example 1: Standard Delivery with Appointment
**Input:**
A shipping document contains:
SHIP TO:
Acme Distribution Center
123 Main St, Suite 400
Springfield, IL 62704
Contact: John Doe
Phone: ************
Email: <EMAIL>
Business Hours: Mon-Fri, 8:00 AM - 5:00 PM
Delivery Appointment: 08/30/2025, 1:00PM-3:00PM CST
Special Instructions: Call 30 minutes before arrival
Delivery PO: DEL-987654
Main Shipment BOL: BOL-555123

**Output:**
{
	"consignee": {
		"name": "Acme Distribution Center",
		"addressLine1": "123 Main St",
		"addressLine2": "Suite 400",
		"city": "Springfield",
		"state": "IL",
		"zipCode": "62704",
		"country": "US",
		"contact": "John Doe",
		"phone": "************",
		"email": "<EMAIL>",
		"businessHours": "Mon-Fri, 8:00 AM - 5:00 PM",
		"refNumber": "DEL-987654",
		"mustDeliver": "08/30/2025, 03:00PM",
		"apptType": "By appointment",
		"apptStartTime": "08/30/2025, 01:00PM",
		"apptEndTime": "08/30/2025, 03:00PM",
		"apptNote": "Call 30 minutes before arrival",
		"timezone": "CST",
		"externalTMSID": null
	}
}

### Example 2: Multiple Reference Numbers
**Input:**
Stop 2 (drop)
Moove, 8120 S. Orange Avenue, Orlando, FL 32809
Mike Dvorak Phone: ************
Delivery: 09/15/2025 07:00AM - 09/15/2025 03:00PM

- SN834918 (BOL)
- 2608089680 (Delivery/Order Number)
- 4700829 (PO Number)
- LD385193 (Load ID)
- LD385193 (PRO)

**Output:**
{
	"consignee": {
		"name": "Moove",
		"addressLine1": "8120 S. Orange Avenue",
		"addressLine2": "",
		"city": "Orlando",
		"state": "FL",
		"zipCode": "32809",
		"country": "US",
		"contact": "Mike Dvorak",
		"phone": "************",
		"email": "",
		"businessHours": "",
		"refNumber": "2608089680",
		"mustDeliver": "09/15/2025, 03:00PM",
		"apptType": "By appointment",
		"apptStartTime": "09/15/2025, 07:00AM",
		"apptEndTime": "09/15/2025, 03:00PM",
		"apptNote": "",
		"timezone": "",
		"externalTMSID": null
	}
}

### Example 3: Multiple Stops - Extract Final Destination
**Input:**
Stop 1 - Intermediate Stop:
Gamma Logistics Hub
789 Transfer Ave
Denver, CO 80202

Final Destination:
Delta Manufacturing
321 Factory Road
Salt Lake City, UT 84101
Contact: Robert Kim
Phone: ************
Delivery Window: 09/02/2025, 10:00AM-12:00PM MST

**Output:**
{
	"consignee": {
		"name": "Delta Manufacturing",
		"addressLine1": "321 Factory Road",
		"addressLine2": "",
		"city": "Salt Lake City",
		"state": "UT",
		"zipCode": "84101",
		"country": "US",
		"contact": "Robert Kim",
		"phone": "************",
		"email": "",
		"businessHours": "",
		"refNumber": "",
		"mustDeliver": "09/02/2025, 12:00PM",
		"apptType": "By appointment",
		"apptStartTime": "09/02/2025, 10:00AM",
		"apptEndTime": "09/02/2025, 12:00PM",
		"apptNote": "",
		"timezone": "MST",
		"externalTMSID": null
	}
}

%s
    `, options.Service.Name, options.Service.EmailDomains, tmsSpecificInstructions, dateTimeNotes, GenericInstructions)

	// Append the historical data to the prompt if it exists.
	if len(historicalLoadData) > 0 {
		historicalDataStr := ""
		for _, load := range historicalLoadData {
			if load.ConsigneeRefNumber != "" {
				historicalDataStr += fmt.Sprintf(" - refNumber: %s\n", load.ConsigneeRefNumber)
			}
		}

		//nolint:lll
		prompt += fmt.Sprintf(`
### **Historical Data for Pattern Recognition**
The following are examples of real, confirmed consignee/delivery reference numbers. Analyze these patterns for:
1. Length: How many characters are typically used
2. Format: Whether it uses only numbers, mix of letters/numbers, or special characters
3. Prefix patterns: Common starting characters or sequences (e.g., "DEL-", "CONS-")
4. Numeric patterns: Position and length of numeric sequences
5. Delimiter usage: Common separators like '-', '_', or '/'

Historical examples:
%s

Use these patterns to validate new consignee/delivery reference numbers against the established format. You should NOT use this historical data in your response, they are only meant to serve as a reference.
`, historicalDataStr)
	}

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForExtractor(
		ctx,
		tms.ID,
		customerID,
		"loadBuilding",
		"consignee",
		prompt,
	)
	if err != nil {
		log.Warn(ctx, "error building custom consignee prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	// Prepare reference number metadata for Braintrust
	extraMetadata := map[string]any{
		"historical_consignee_ref_numbers": historicalLoadData,
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[ConsigneeSchema](),
			PreviousResponseID: previousResponseID,
			Temperature:        openai.TemperatureDeterministic,
			ExtraMetadata:      extraMetadata,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[ConsigneeInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	if tms.Name == models.Aljex {
		// Aljex rejects phones with country codes, and FE's FormatPhoneNumber changes the number by
		// converting the country code to the leading digit.
		result.Consignee.Phone, err = FormatPhoneCustom(result.Consignee.Phone)
		if err != nil {
			log.WarnNoSentry(ctx, "error formatting phone number", zap.Error(err))
		}
	} else {

		if tms.Name == models.McleodEnterprise {
			// McLeodEnterprise: Use mustDeliver as fallback for appointment times if they're not set
			if !result.Consignee.ApptStartTime.Valid && result.Consignee.MustDeliver.Valid {
				result.Consignee.ApptStartTime = result.Consignee.MustDeliver
			}
		}

		tz, err := timezone.GetTimezoneByZipOrCity(ctx,
			result.Consignee.Zipcode, result.Consignee.City, result.Consignee.State, "")
		if err != nil {
			log.WarnNoSentry(ctx, "error getting consignee timezone", zap.Error(err))
		} else {
			// Start time and end time are in UTC, so we need to denormalize them to the consignee's timezone
			denormalizedTime, err := timezone.DenormalizeUTC(
				result.Consignee.ApptStartTime.Time, tz)
			if err == nil {
				result.Consignee.ApptStartTime.Time = denormalizedTime
			}

			denormalizedTime, err = timezone.DenormalizeUTC(
				result.Consignee.ApptEndTime.Time, tz)
			if err == nil {
				result.Consignee.ApptEndTime.Time = denormalizedTime
			}

			denormalizedTime, err = timezone.DenormalizeUTC(result.Consignee.MustDeliver.Time, tz)
			if err == nil {
				result.Consignee.MustDeliver.Time = denormalizedTime
			}

		}
	}

	result.Consignee.CompanyCoreInfo = ValidateStop(ctx, "consignee", result.Consignee.CompanyCoreInfo, tms.ID)

	return result, rawResult, response.BraintrustLogID, nil
}

// extractSpecifications extracts specification information from either an email body or attachment
func extractSpecifications(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	attachment models.Attachment,
	tms models.Integration,
	customerID uint,
	previousResponseID string,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	options Options,
) (result SpecificationsInfo, rawResult SpecificationsInfo, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractSpecifications", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	//nolint:lll
	prompt := fmt.Sprintf(`
You are an expert logistics data extractor. Your primary task is to extract shipment specifications from the provided text and format them into a single, valid JSON object.

### **Output Format**
Your response MUST be a single JSON object that strictly follows this structure:

{
	"specifications": {
		"serviceType": "Any",
		"transportType": "VAN",
		"transportSize": "53 ft",
		"totalInPalletCount": 0,
		"commodities": "",
		"totalWeight": {
			"val": 0,
			"unit": "lb"
		},
		"billableWeight": {
			"val": 0,
			"unit": "lb"
		},
		"minTempFahrenheit": 0,
		"maxTempFahrenheit": 0,
		"totalPieces": {
			"val": 0,
			"unit": "Units"
		},
		"planningComment": ""
	}
}

### **Extraction Rules**
Apply these rules when extracting data:

* **serviceType**: Always set to "Any".

* **transportType**: Determine the correct transport type.
    * **CRITICAL RULE**: If the text contains any indication of a temperature-controlled environment, you MUST set this field to **"REEFER"**. Look for explicit mentions of temperature (e.g., "degrees," "temp"), refrigeration, chilling, or freezing. Also, look for phrases describing the product, such as "chilled beef," "frozen goods," or "refrigerated produce." Any reference to "freezer locations" or maintaining a specific temperature is a definitive signal.
    * If no temperature-related requirement is found, check for other specified types like **"VAN"**, **"FLATBED"**, **"BOX TRUCK"**, or **"HOTSHOT"**.
    * If no type is specified, default to **"VAN"**.

* **transportSize**: Extract the vehicle size, such as "53 ft". If no size is specified, use these defaults based on transport type:
    * **VAN** or **REEFER**: Default to **"53 ft"**
    * **FLATBED**: Default to **"48 ft"**
    * All other transport types: Default to an empty string

* **totalInPalletCount**: Extract the number of pallets or crates. If none are found, default to **0**.

* **totalPieces**: Extract the total quantity/number of items/units being shipped. 
	Look for terms like "pieces", "units", "items", "qty", "quantity", "count".
	Examples: "585 PC" → 585 pieces, "24 units" → 24 units, "100 boxes" → 100 boxes.
	If unit type is specified (boxes, pieces, units, etc.), include it in the unit field.
	ALWAYS use proper casing for the unit type. ie "Boxes" not "boxes"
	If there are multiple line items with the same name/type, sum the quantities.

* **commodities**: Provide a brief description of the goods being shipped.

* **Weights**: Extract "totalWeight" and "billableWeight" and their corresponding units. Default to "lb" if a unit is not specified. If no weight value is found, default to **0**.

* **Temperatures**: For REEFER loads, extract the minimum and maximum temperatures in Fahrenheit. The example text may provide a single temperature, in which case set both "minTempFahrenheit" and "maxTempFahrenheit" to that value. If no temperature is specified for a REEFER load, set both "minTempFahrenheit" and "maxTempFahrenheit" to **0**.

* **Planning Comment**: Any special instructions, handling requirements, or delivery notes


%s`, GenericInstructions)

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForExtractor(
		ctx,
		tms.ID,
		customerID,
		"loadBuilding",
		models.PromptExtractorNameSpecifications,
		prompt,
	)
	if err != nil {
		log.Warn(ctx, "error building custom specifications prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[SpecificationsSchema](),
			PreviousResponseID: previousResponseID,
			// TODO: Enable temperature for all extraction prompts when we have the time to test it.
			// Temperature:     openai.TemperatureDeterministic,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[SpecificationsInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	result.Specifications.TransportType = string(
		validateTransportType(
			ctx, models.TransportType(result.Specifications.TransportType), userPrompt, options.Config),
	)

	// @Sophie plans to push code to introduce a solution to handle TMS specific fields.
	// For now, we conditionally set fields based on the TMS;

	switch tms.Name {
	case models.Turvo:
		turvoSpecs := turvo.BuildTurvoSpecifications(result.Specifications)
		result.Specifications = turvoSpecs
	case models.McleodEnterprise:
		// associate commodities with their Mcleod commodity code
		mcleodCommodityCode := mcleodenterprise.ToTMSCommodity(ctx, tms.Tenant, result.Specifications.Commodities)
		result.Specifications.Commodities = mcleodCommodityCode
	}

	return result, rawResult, response.BraintrustLogID, nil
}

// extracts Aljex-specific detailed commodities, additional references, and special instructions
// this function specific to aljex
func extractCommoditiesAndRefs(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	tmsObj models.Integration,
	attachment models.Attachment,
	customer models.TMSCustomer,
	previousResponseID string,
	_ string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (
	result CommoditiesAndRefsInfo,
	rawResult CommoditiesAndRefsInfo,
	braintrustLogID string,
	err error,
) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractCommoditiesAndRefs", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	//nolint:lll
	prompt := fmt.Sprintf(`
    You are an expert logistics data extractor. Based on the shipping information from our previous conversation, extract commodity items, additional reference numbers, and notes and format them into a single, valid JSON object.

    COMMODITIES: Extract individual commodity items with full details:
    - description: Specific product description
    - quantity: Number of units/pieces
    - weightTotal: Total weight per commodity - extract EXACTLY as mentioned in the email
      (preserve original units and values. Do not convert, round, or sum up different line items into one row)
    - length, width, height: Dimensions - extract EXACTLY as mentioned in the email
      (preserve original units and values, do not convert, round, or sum up different line items into one line item)
    - referenceNumber: Product SKU, item number, or product code

    ADDITIONAL REFERENCES: Extract all reference numbers and identifiers:
    - Bill of Lading (BOL) numbers
    - Load numbers
    - Purchase Order (PO) numbers
    - Release numbers
    - Shipment tracking numbers
    - Customer reference numbers
    - Any other identifying codes

    SPECIAL INSTRUCTIONS: Extract any special handling, pickup, or delivery instructions:
    - Temperature requirements
    - Handling instructions (fragile, hazmat, etc.)
    - Delivery requirements (liftgate, appointment, etc.)
    - Pickup instructions
    - General notes or comments

    Format your response as a JSON object with:
    {
      "commodities": [
        {
          "description": "Product description",
          "quantity": 0,
          "weightTotal": 0.0,
          "length": 0.0,
          "width": 0.0,
          "height": 0.0,
          "referenceNumber": ""
        }
      ],
      "additionalReferences": [
        {
          "qualifier": "BOL",
          "number": "1234",
        },
        {
          "qualifier": "LOAD",
          "number": "4567",
        },
        {
          "qualifier": "PO",
          "number": "7890",
        },
        {
          "qualifier": "REF",
          "number": "1234",
        }

      ],
    }

	%s
    `, GenericInstructions)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[CommoditiesAndRefsSchema](),
			PreviousResponseID: previousResponseID,
			UserPrompt:         "", // Not needed, already in conversation context
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[CommoditiesAndRefsInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	result.Commodities = validateAljexCommodities(ctx, &tmsObj, customer.Name, result.Commodities)

	return result, rawResult, response.BraintrustLogID, nil
}

// IsAttachmentNewLoadInformation checks if a userPrompt contains load building or quote request information
// We currently do not use this function, but it is kept here for future reference.
func IsAttachmentNewLoadInformation(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (isLoadBuilding bool, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "IsAttachmentNewLoadInformation", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	//nolint:lll
	prompt := `
	You are an expert logistics email analyzer. Your task is to identify if this email contains load building or quote request information.

	Load building definition:
	- load building: About creating/building a new load or order with specific details
			Detailed Example Email:
				Subject: New shipment details - please create order

				Warehouse Code:
				Address:
				115-0010
				Ship To:
				5019494
				08/21/2024
				Shipping Order
				Shipping Order No.:
				Order Date:
				Customer PO:
				Ship From:
				Kronospan PB, LLC
				Kronospan PB, LLC
				1 Kronospan Way
				Eastaboga, AL United States
				MJB Anniston
				1608 Frank Akers Road
				Anniston, AL 36207
				1585 High Meadows Way
				Cedar Hill, TX 75104
				www.mjbwood.com
				************
				Page 1 of 1
				Phone No.:
				Phone No.:
				Salesperson
				Ship Mode
				Freight Terms
				VAN
				MILL
				Due Date
				09/30/2024
				Order Qty.
				Balance
				Received
				Cust. Part No.
				Description
				Footage
				Est. Weight
				Line Item Ref. No.
				Qty. / Pallet
				585
				45
				585 PC
				500544 - 5/8" x 49" x 97" PBD Raw
				Kronospan-Eastaboga PBD(95086)
				0.00 SF
				41,842.73 lbs
				Location:
				Shipping Notes:
				Total Footage:
				Total Estimated Weight:
				Carrier Name:
				Only the products that are identified as such on this document are FSC® certified.
				Unique Item Count:
				Total Qty.:
				1
				585 PC
				Carrier is to deliver to specified "Ship To" address only. If a change of delivery address is requested, please contact your
				MJB Wood Group representative.
				EPA TSCA Title VI & CARB 93120 Ph2 Compliant, for applicable products.
				Load must be 100% tarped - no exposed material. Load must be completely tarped before leaving the loading facility -
				do NOT untarp until instructed to do so by the receiver.
				0.00 SF
				41,842.73 lbs
				Total Pallets:
				13

	Quote request definition:
	- quote request: Explicitly requests pricing for shipping services
		Detailed Example Email:
			Subject: Need rate for Orlando to Miami shipment ASAP

					Hello,

					We are looking for a competitive rate for the following shipment:

					Origin: Orlando, FL
					Destination: Miami, FL
					Pickup Date: July 25, 2023
					Delivery Date: July 26, 2023
					Commodity: Electronics (palletized)
					Weight: 5,000 lbs
					Dimensions: 4 pallets, 48"x40"x50" each
					Special Requirements: Liftgate needed at delivery

					Can you please provide your best rate for this lane? We need to book this shipment by tomorrow.

					Thank you,
					Procurement Team



    Answer with ONLY "YES" if this is a new load or quote request document or "NO" if it is not.
    `

	response, err := openaiService.GetResponse(
		ctx,
		email,
		models.Attachment{},
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt: prompt,
			UserPrompt:      userPrompt,
			// TODO: Enable temperature for all extraction prompts when we have the time to test it.
			// Temperature:     openai.TemperatureDeterministic,
		},
	)
	if err != nil {
		return false, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	return strings.Contains(strings.ToUpper(response.Content), "YES"), response.BraintrustLogID, nil
}

// getTurvoAppointmentInstructions returns TMS-specific instructions for Turvo appointment handling
func getTurvoAppointmentInstructions(locationType string) string {
	locationContext := "shipment information"
	if locationType == "delivery" {
		locationContext = "shipment delivery information"
	}

	return fmt.Sprintf(`
		Additional instructions on the apptType, apptStartTime, and apptEndTime fields:
		- If "FCFS" is mentioned anywhere in the %s, mark apptType as "FCFS".
		- If there is NO mention of "FCFS" in the %s, but there is a %s date 
			(like 8/26/25) AND an associated time (like 15:00), mark apptType as "By appointment".
		- If there is only a date (like 8/26/25) and no associated time (like 15:00), just set the apptStartTime 
			and apptEndTime to be that date.
		- If there is a time associated with the %s date (such as 15:00-16:00), mark the former as
			apptStartTime and the latter as apptEndTime.
	    - If there are no dates associated with the %s information,  simply set apptType as an empty string """
		`, locationContext, locationContext, locationType, locationType, locationType)
}

func validateAljexCommodities(
	ctx context.Context,
	tmsObj *models.Integration,
	customerName string,
	items []models.Commodity,
) (result []models.Commodity) {
	_, metaSpan := otel.StartSpan(ctx, "validateAljexCommodities", otel.IntegrationAttrs(*tmsObj))
	defer func() { metaSpan.End(nil) }()

	if tmsObj.Name != models.Aljex || !strings.EqualFold(tmsObj.Tenant, "abts") {
		return items
	}

	customerName = strings.ToLower(customerName)
	if !strings.Contains(customerName, "ptr") && !strings.Contains(customerName, "premier truck") {
		return items
	}

	// Because of Aljex's stringent character limits, Able has a unique way of circumventing them:
	// they list the dimensions unit (ft, in) in the commodity.ReferenceNumber aka ProductCode field.
	// Then they add dummy rows to complete the description.
	// See load 112827 for an example, and https://shorturl.at/cgoRb at 9-min mark
	const descriptionCharLimit = 40

	for _, item := range items {
		originalDescription := item.Description
		originalReferenceNumber := item.ReferenceNumber

		newDescription := originalDescription + "," + originalReferenceNumber

		if len(strings.TrimSpace(newDescription)) <= descriptionCharLimit {
			newItem := item
			newItem.ReferenceNumber = item.DimensionsUnit
			newItem.Description = newDescription
			result = append(result, newItem)

			continue
		}

		splitDescriptions := strings.Split(newDescription, ",")
		for i, splitDescription := range splitDescriptions {
			if i == 0 {
				newItem := item
				newItem.ReferenceNumber = item.DimensionsUnit
				newItem.Description = splitDescription
				result = append(result, newItem)
			} else {
				newItem := models.Commodity{
					Description: splitDescription,
				}
				result = append(result, newItem)
			}
		}
	}

	return result
}
