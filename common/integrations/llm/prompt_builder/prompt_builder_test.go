package promptbuilder

import (
	"context"
	"errors"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

func TestBuildPromptForTMS(t *testing.T) {
	ctx := context.Background()
	defaultPrompt := "Default prompt"

	// Mock the DB function
	getActiveTMSPromptsFunc = func(_ context.Context, tmsID uint, feature string) ([]models.TMSCustomerPrompt, error) {
		if tmsID == 1 && feature == "loadBuilding" {
			return []models.TMSCustomerPrompt{
				{ExtractorName: "basicInfo", PromptText: "TMS-wide prompt"},
				{ExtractorName: "anotherExtractor", PromptText: "Should not be included"},
			}, nil
		}
		if tmsID == 2 && feature == "loadBuilding" {
			return []models.TMSCustomerPrompt{
				{ExtractorName: "basicInfo", PromptText: "First TMS prompt"},
				{ExtractorName: "basicInfo", PromptText: "Second TMS prompt"},
			}, nil
		}
		if tmsID == 999 {
			return nil, errors.New("db error")
		}
		return nil, nil
	}

	t.Run("should fail if getting tms-wide prompts fails", func(t *testing.T) {
		result, err := BuildPromptForTMS(ctx, 999, "loadBuilding", "basicInfo", defaultPrompt)
		require.Error(t, err)
		require.Empty(t, result)
	})

	t.Run("should combine default and tms-wide prompts", func(t *testing.T) {
		result, err := BuildPromptForTMS(ctx, 1, "loadBuilding", "basicInfo", defaultPrompt)
		require.NoError(t, err)
		expected := "Default prompt\n\nTMS-wide prompt"
		require.Equal(t, expected, result)
	})

	t.Run("should return default prompt if no tms prompts match extractor name", func(t *testing.T) {
		result, err := BuildPromptForTMS(ctx, 1, "loadBuilding", "nonExistentExtractor", defaultPrompt)
		require.NoError(t, err)
		require.Equal(t, defaultPrompt, result)
	})

	t.Run("should combine multiple tms-wide prompts", func(t *testing.T) {
		result, err := BuildPromptForTMS(ctx, 2, "loadBuilding", "basicInfo", defaultPrompt)
		require.NoError(t, err)
		expected := "Default prompt\n\nFirst TMS prompt\n\nSecond TMS prompt"
		require.Equal(t, expected, result)
	})

	t.Run("should fail if prompt exceeds token limit", func(t *testing.T) {
		longPrompt := strings.Repeat("word ", MaxPromptTokens)
		getActiveTMSPromptsFunc = func(
			context.Context,
			uint,
			string,
		) ([]models.TMSCustomerPrompt, error) {
			return []models.TMSCustomerPrompt{
				{ExtractorName: "basicInfo", PromptText: longPrompt},
			}, nil
		}
		result, err := BuildPromptForTMS(ctx, 1, "loadBuilding", "basicInfo", "default")
		require.Error(t, err)
		require.Contains(t, err.Error(), "prompt exceeds maximum token count")
		require.Empty(t, result)
	})
}

func TestBuildPromptForExtractor(t *testing.T) {
	ctx := context.Background()
	defaultPrompt := "Default prompt"

	// Mock the DB functions
	getActiveCustomerPromptsFunc = func(
		_ context.Context,
		tmsID,
		customerID uint,
		feature string,
	) ([]models.TMSCustomerPrompt, error) {
		if tmsID == 1 && customerID == 123 && feature == "loadBuilding" {
			return []models.TMSCustomerPrompt{
				{ExtractorName: "basicInfo", PromptText: "Customer-specific prompt"},
			}, nil
		}
		if tmsID == 1 && customerID == 234 && feature == "loadBuilding" {
			return []models.TMSCustomerPrompt{
				{ExtractorName: "basicInfo", PromptText: "First customer prompt"},
				{ExtractorName: "basicInfo", PromptText: "Second customer prompt"},
			}, nil
		}
		if tmsID == 1 && customerID == 789 && feature == "loadBuilding" {
			return nil, errors.New("database error")
		}
		if tmsID == 2 && customerID == 111 && feature == "loadBuilding" {
			return []models.TMSCustomerPrompt{
				{ExtractorName: "basicInfo", PromptText: "Customer prompt with no TMS prompt"},
			}, nil
		}
		return nil, nil // Default case for other customers
	}
	getActiveTMSPromptsFunc = func(_ context.Context, tmsID uint, feature string) ([]models.TMSCustomerPrompt, error) {
		if tmsID == 1 && feature == "loadBuilding" {
			return []models.TMSCustomerPrompt{
				{ExtractorName: "basicInfo", PromptText: "TMS-wide prompt"},
			}, nil
		}
		if tmsID == 999 {
			return nil, errors.New("db error")
		}
		return nil, nil // Default case for other TMS
	}

	t.Run("should fail if getting tms-wide prompts fails", func(t *testing.T) {
		result, err := BuildPromptForExtractor(ctx, 999, 999, "loadBuilding", "basicInfo", defaultPrompt)
		require.Error(t, err)
		require.Empty(t, result)
	})

	t.Run("should combine default, tms-wide, and customer-specific prompts", func(t *testing.T) {
		result, err := BuildPromptForExtractor(ctx, 1, 123, "loadBuilding", "basicInfo", defaultPrompt)
		require.NoError(t, err)
		expected := "Default prompt\n\nTMS-wide prompt\n\nCustomer-specific prompt"
		require.Equal(t, expected, result)
	})

	t.Run("should combine multiple tms-wide and customer-specific prompts", func(t *testing.T) {
		// This requires temporarily overriding the mock for this specific sub-test
		originalFunc := getActiveTMSPromptsFunc
		getActiveTMSPromptsFunc = func(
			_ context.Context,
			tmsID uint,
			feature string,
		) ([]models.TMSCustomerPrompt, error) {
			if tmsID == 1 && feature == "loadBuilding" {
				return []models.TMSCustomerPrompt{
					{ExtractorName: "basicInfo", PromptText: "First TMS prompt"},
					{ExtractorName: "basicInfo", PromptText: "Second TMS prompt"},
				}, nil
			}
			return nil, nil
		}
		defer func() { getActiveTMSPromptsFunc = originalFunc }() // Restore mock

		result, err := BuildPromptForExtractor(ctx, 1, 234, "loadBuilding", "basicInfo", defaultPrompt)
		require.NoError(t, err)
		//nolint:lll
		expected := "Default prompt\n\nFirst TMS prompt\n\nSecond TMS prompt\n\nFirst customer prompt\n\nSecond customer prompt"
		require.Equal(t, expected, result)
	})

	t.Run("should combine default and customer-specific prompts if no tms-wide prompts exist", func(t *testing.T) {
		result, err := BuildPromptForExtractor(ctx, 2, 111, "loadBuilding", "basicInfo", defaultPrompt)
		require.NoError(t, err)
		expected := "Default prompt\n\nCustomer prompt with no TMS prompt"
		require.Equal(t, expected, result)
	})

	t.Run("should only use default and tms-wide if no customer-specific prompts", func(t *testing.T) {
		result, err := BuildPromptForExtractor(ctx, 1, 456, "loadBuilding", "basicInfo", defaultPrompt)
		require.NoError(t, err)
		expected := "Default prompt\n\nTMS-wide prompt"
		require.Equal(t, expected, result)
	})

	t.Run("should only fetch tms-wide prompts if customerID is 0", func(t *testing.T) {
		result, err := BuildPromptForExtractor(ctx, 1, 0, "loadBuilding", "basicInfo", defaultPrompt)
		require.NoError(t, err)
		expected := "Default prompt\n\nTMS-wide prompt"
		require.Equal(t, expected, result)
	})

	t.Run("should fail if getting customer prompts fails", func(t *testing.T) {
		result, err := BuildPromptForExtractor(ctx, 1, 789, "loadBuilding", "basicInfo", defaultPrompt)
		require.Error(t, err)
		require.Contains(t, err.Error(), "error getting customer prompts")
		require.Empty(t, result)
	})

	t.Run("should return default prompt if tmsID is 0", func(t *testing.T) {
		result, err := BuildPromptForExtractor(ctx, 0, 0, "loadBuilding", "basicInfo", "default")
		require.NoError(t, err)
		require.Equal(t, "default", result)
	})

	t.Run("should use default prompt if extractor name is empty", func(t *testing.T) {
		result, err := BuildPromptForExtractor(ctx, 1, 123, "loadBuilding", "", "default")
		require.NoError(t, err)
		require.Equal(t, "default", result, "Expected default prompt when extractor name is empty")
	})

	t.Run("should handle empty default prompt", func(t *testing.T) {
		result, err := BuildPromptForExtractor(ctx, 1, 123, "loadBuilding", "basicInfo", "")
		require.NoError(t, err)
		expected := "\n\nTMS-wide prompt\n\nCustomer-specific prompt"
		require.Equal(t, expected, result)
	})

	t.Run("should fail if prompt exceeds token limit", func(t *testing.T) {
		longPrompt := strings.Repeat("word ", MaxPromptTokens)
		// Temporarily override mock to return a very long prompt
		originalFunc := getActiveCustomerPromptsFunc
		getActiveCustomerPromptsFunc = func(context.Context, uint, uint, string) ([]models.TMSCustomerPrompt, error) {
			return []models.TMSCustomerPrompt{
				{ExtractorName: "basicInfo", PromptText: longPrompt},
			}, nil
		}
		defer func() { getActiveCustomerPromptsFunc = originalFunc }() // Restore mock

		result, err := BuildPromptForExtractor(ctx, 1, 123, "loadBuilding", "basicInfo", "default")
		require.Error(t, err)
		require.Contains(t, err.Error(), "prompt exceeds maximum token count")
		require.Empty(t, result)
	})
}
