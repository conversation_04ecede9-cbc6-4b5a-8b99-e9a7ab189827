package dat

import (
	"context"
	"encoding/json"
	"os"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
	rediscommon "github.com/drumkitai/drumkit/common/redis"
)

func TestMain(m *testing.M) {
	// Disable rate limiting for tests to avoid Redis connection issues
	os.Setenv("DISABLE_RATE_LIMIT", "true")

	m.Run()

	os.Unsetenv("DISABLE_RATE_LIMIT")
}

func TestRetrieveRedisClient(t *testing.T) {
	// Create a mock Redis client
	mockRDB, mock := redismock.NewClientMock()
	originalRDB := rediscommon.RDB
	rediscommon.RDB = mockRDB
	defer func() {
		rediscommon.RDB = originalRDB
		_ = mockRDB.Close()
	}()

	ctx := context.Background()
	testEmail := "<EMAIL>"

	tests := []struct {
		name        string
		setupMock   func(mock redismock.ClientMock)
		expectFound bool
		expectError bool
	}{
		{
			name: "client found in Redis",
			setupMock: func(mock redismock.ClientMock) {
				// Redis wrapper JSON-encodes the struct
				mock.ExpectGet("<EMAIL>").SetVal(`{
					"IdentityHost": "identity.dat.com",
					"AnalyticsHost": "analytics.dat.com", 
					"UserEmail": "<EMAIL>",
					"OrgToken": {"AccessToken": "org-token-123", "ExpiresWhen": "2024-01-01T12:00:00Z"},
					"UserToken": {"AccessToken": "user-token-456", "ExpiresWhen": "2024-01-01T12:00:00Z"}
				}`)
			},
			expectFound: true,
			expectError: false,
		},
		{
			name: "client not found in Redis",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectGet("<EMAIL>").RedisNil()
			},
			expectFound: false,
			expectError: false,
		},
		{
			name: "Redis connection error",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectGet("<EMAIL>").SetErr(assert.AnError)
			},
			expectFound: false,
			expectError: false, // Function handles Redis errors gracefully
		},
		{
			name: "invalid JSON in Redis",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectGet("<EMAIL>").SetVal(`{"invalid json"}`)
			},
			expectFound: false,
			expectError: false, // Function handles JSON errors gracefully
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock expectations
			tt.setupMock(mock)

			// Execute function
			client := retrieveRedisClient(ctx, testEmail)

			// Assert results
			if tt.expectFound {
				require.NotNil(t, client)
				assert.Equal(t, "identity.dat.com", client.identityHost)
				assert.Equal(t, "analytics.dat.com", client.analyticsHost)
				assert.Equal(t, testEmail, client.userEmail)
				assert.Equal(t, "org-token-123", client.orgToken.AccessToken)
				assert.Equal(t, "user-token-456", client.userToken.AccessToken)
			} else {
				assert.Nil(t, client)
			}

			// Verify all Redis expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestCacheClient(t *testing.T) {
	// Create a mock Redis client
	mockRDB, mock := redismock.NewClientMock()
	originalRDB := rediscommon.RDB
	rediscommon.RDB = mockRDB
	defer func() {
		rediscommon.RDB = originalRDB
		_ = mockRDB.Close()
	}()

	ctx := context.Background()
	testEmail := "<EMAIL>"

	// Create a test client
	client := &Client{
		identityHost:  "identity.dat.com",
		analyticsHost: "analytics.dat.com",
		userEmail:     testEmail,
		orgToken: ClientToken{
			AccessToken: "org-token-123",
			ExpiresWhen: models.ToValidNullTime(time.Now().Add(time.Hour)),
		},
		userToken: ClientToken{
			AccessToken: "user-token-456",
			ExpiresWhen: models.ToValidNullTime(time.Now().Add(time.Hour)),
		},
	}

	tests := []struct {
		name      string
		setupMock func(mock redismock.ClientMock)
	}{
		{
			name: "successfully cache client",
			setupMock: func(mock redismock.ClientMock) {
				mock.Regexp().ExpectSet("<EMAIL>", `.*`, 30*time.Minute).SetVal("OK")
			},
		},
		{
			name: "Redis set error handled gracefully",
			setupMock: func(mock redismock.ClientMock) {
				mock.Regexp().ExpectSet("<EMAIL>", `.*`, 30*time.Minute).SetErr(assert.AnError)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock(mock)

			client.cacheClient(ctx)

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestRedisClientKey(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		expected string
	}{
		{
			name:     "normal email",
			email:    "<EMAIL>",
			expected: "<EMAIL>",
		},
		{
			name:     "uppercase email gets lowercased",
			email:    "<EMAIL>",
			expected: "<EMAIL>",
		},
		{
			name:     "mixed case email gets lowercased",
			email:    "<EMAIL>",
			expected: "<EMAIL>",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := redisClientKey(tt.email)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSerializableDATClient(t *testing.T) {
	// Test that our SerializableDATClient struct can be properly JSON marshaled/unmarshaled
	original := SerializableDATClient{
		IdentityHost:  "identity.dat.com",
		AnalyticsHost: "analytics.dat.com",
		UserEmail:     "<EMAIL>",
		OrgToken: ClientToken{
			AccessToken: "org-token-123",
			ExpiresWhen: models.ToValidNullTime(time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)),
		},
		UserToken: ClientToken{
			AccessToken: "user-token-456",
			ExpiresWhen: models.ToValidNullTime(time.Date(2024, 1, 1, 13, 0, 0, 0, time.UTC)),
		},
	}

	// This simulates what the Redis wrapper does internally
	jsonData, err := json.Marshal(original)
	require.NoError(t, err)

	var unmarshaled SerializableDATClient
	err = json.Unmarshal(jsonData, &unmarshaled)
	require.NoError(t, err)

	// Verify all fields are preserved
	assert.Equal(t, original.IdentityHost, unmarshaled.IdentityHost)
	assert.Equal(t, original.AnalyticsHost, unmarshaled.AnalyticsHost)
	assert.Equal(t, original.UserEmail, unmarshaled.UserEmail)
	assert.Equal(t, original.OrgToken.AccessToken, unmarshaled.OrgToken.AccessToken)
	assert.Equal(t, original.UserToken.AccessToken, unmarshaled.UserToken.AccessToken)

	// NullTime should serialize/deserialize correctly
	assert.True(t, original.OrgToken.ExpiresWhen.Valid)
	assert.True(t, unmarshaled.OrgToken.ExpiresWhen.Valid)
	assert.Equal(t, original.OrgToken.ExpiresWhen.Time.Unix(), unmarshaled.OrgToken.ExpiresWhen.Time.Unix())
}
