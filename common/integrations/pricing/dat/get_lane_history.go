package dat

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
)

const laneHistoryEndpoint = "/linehaulrates/v1/history"

func (d *Client) GetLaneHistory(ctx context.Context, reqBody *GetLaneHistoryRequest) (*GetLaneHistoryResponse, error) {
	var result GetLaneHistoryResponse

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	body := bytes.NewBuffer(jsonData)
	reqAuth := fmt.Sprintf("Bearer %s", d.userToken.AccessToken)
	if _, err = d.post(ctx, laneHistoryEndpoint, body, &result, &reqAuth); err != nil {
		return nil, err
	}

	return &result, nil
}
