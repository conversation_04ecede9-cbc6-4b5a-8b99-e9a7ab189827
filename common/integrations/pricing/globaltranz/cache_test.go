package globaltranz

import (
	"context"
	"encoding/json"
	"os"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	rediscommon "github.com/drumkitai/drumkit/common/redis"
)

func TestMain(m *testing.M) {
	os.Setenv("DISABLE_RATE_LIMIT", "true")
	m.Run()
	os.Unsetenv("DISABLE_RATE_LIMIT")
}

func TestGetRedisClient(t *testing.T) {
	ctx := context.Background()

	t.Run("client found", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Test data
		expectedSGT := SerializableGlobalTranzClient{
			AccessToken: "test-access-token",
			Integration: models.Integration{
				ServiceID: 1,
				Model:     gorm.Model{ID: 2},
				Username:  "testuser",
				Tenant:    "testtenant",
			},
			AuthHost:        "is.globaltranz.com",
			TMSHost:         "tms.globaltranz.com",
			AddressBookHost: "gtz-wus-tms-addressbook-api-pd.azurewebsites.net",
			MainSystemHost:  "gtz-wus-tms-systemadmin-api-pd.azurewebsites.net",
		}

		jsonBytes, err := json.Marshal(expectedSGT)
		require.NoError(t, err)

		mock.ExpectGet("globaltranz-service-1-integration-2").SetVal(string(jsonBytes))

		// Execute
		client := getRedisClient(ctx, 1, 2)

		// Verify
		require.NotNil(t, client)
		assert.Equal(t, expectedSGT.AccessToken, client.accessToken)
		assert.Equal(t, expectedSGT.Integration.ServiceID, client.integration.ServiceID)
		assert.Equal(t, expectedSGT.Integration.Username, client.integration.Username)
		assert.Equal(t, expectedSGT.AuthHost, client.authHost)
		assert.Equal(t, expectedSGT.TMSHost, client.tmsHost)
		assert.Equal(t, expectedSGT.AddressBookHost, client.addressBookHost)
		assert.Equal(t, expectedSGT.MainSystemHost, client.mainSystemHost)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("client not found", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		mock.ExpectGet("globaltranz-service-1-integration-2").RedisNil()

		// Execute
		client := getRedisClient(ctx, 1, 2)

		// Verify
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("redis error", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		mock.ExpectGet("globaltranz-service-1-integration-2").SetErr(assert.AnError)

		// Execute
		client := getRedisClient(ctx, 1, 2)

		// Verify
		// Should not return error for Redis failures
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("invalid json", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		mock.ExpectGet("globaltranz-service-1-integration-2").SetVal("invalid json")

		// Execute
		client := getRedisClient(ctx, 1, 2)

		// Verify
		// Should not return error for JSON unmarshal failures
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCacheClient(t *testing.T) {
	ctx := context.Background()

	t.Run("successful cache", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Create test client
		client := &Client{
			accessToken: "test-access-token",
			integration: models.Integration{
				ServiceID: 1,
				Model:     gorm.Model{ID: 2},
				Username:  "testuser",
				Tenant:    "testtenant",
			},
			authHost:        "is.globaltranz.com",
			tmsHost:         "tms.globaltranz.com",
			addressBookHost: "gtz-wus-tms-addressbook-api-pd.azurewebsites.net",
			mainSystemHost:  "gtz-wus-tms-systemadmin-api-pd.azurewebsites.net",
		}

		// Expect any JSON value to be set with 40 minute expiration
		mock.Regexp().ExpectSet("globaltranz-service-1-integration-2", `.*`, 40*time.Minute).SetVal("OK")

		// Execute
		client.cacheClient(ctx)

		// Verify
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("redis set error", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Create test client
		client := &Client{
			accessToken: "test-access-token",
			integration: models.Integration{
				ServiceID: 1,
				Model:     gorm.Model{ID: 2},
			},
			authHost:        "is.globaltranz.com",
			tmsHost:         "tms.globaltranz.com",
			addressBookHost: "gtz-wus-tms-addressbook-api-pd.azurewebsites.net",
			mainSystemHost:  "gtz-wus-tms-systemadmin-api-pd.azurewebsites.net",
		}

		// Expect set to fail
		mock.Regexp().ExpectSet("globaltranz-service-1-integration-2", `.*`, 40*time.Minute).SetErr(assert.AnError)

		// Execute (should not panic or return error)
		client.cacheClient(ctx)

		// Verify
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRedisClientKey(t *testing.T) {
	tests := []struct {
		name          string
		serviceID     uint
		integrationID uint
		expected      string
	}{
		{"basic case", 1, 2, "globaltranz-service-1-integration-2"},
		{"zero values", 0, 0, "globaltranz-service-0-integration-0"},
		{"large values", 9999, 8888, "globaltranz-service-9999-integration-8888"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := redisClientKey(tt.serviceID, tt.integrationID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSerializableGlobalTranzClient(t *testing.T) {
	t.Run("json serialization round trip", func(t *testing.T) {
		original := SerializableGlobalTranzClient{
			AccessToken: "test-access-token-123",
			Integration: models.Integration{
				ServiceID: 42,
				Model:     gorm.Model{ID: 99},
				Username:  "testuser",
				Tenant:    "testtenant",
			},
			AuthHost:        "is.globaltranz.com",
			TMSHost:         "tms.globaltranz.com",
			AddressBookHost: "gtz-wus-tms-addressbook-api-pd.azurewebsites.net",
			MainSystemHost:  "gtz-wus-tms-systemadmin-api-pd.azurewebsites.net",
		}

		// Marshal to JSON
		jsonBytes, err := json.Marshal(original)
		require.NoError(t, err)

		// Unmarshal back
		var restored SerializableGlobalTranzClient
		err = json.Unmarshal(jsonBytes, &restored)
		require.NoError(t, err)

		// Verify
		assert.Equal(t, original.AccessToken, restored.AccessToken)
		assert.Equal(t, original.Integration.ServiceID, restored.Integration.ServiceID)
		assert.Equal(t, original.Integration.ID, restored.Integration.ID)
		assert.Equal(t, original.Integration.Username, restored.Integration.Username)
		assert.Equal(t, original.Integration.Tenant, restored.Integration.Tenant)
		assert.Equal(t, original.AuthHost, restored.AuthHost)
		assert.Equal(t, original.TMSHost, restored.TMSHost)
		assert.Equal(t, original.AddressBookHost, restored.AddressBookHost)
		assert.Equal(t, original.MainSystemHost, restored.MainSystemHost)
	})
}
