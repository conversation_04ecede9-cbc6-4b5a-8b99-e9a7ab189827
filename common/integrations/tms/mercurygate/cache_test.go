package mercurygate

import (
	"context"
	"encoding/json"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	rediscommon "github.com/drumkitai/drumkit/common/redis"
)

func TestMain(m *testing.M) {
	os.Setenv("DISABLE_RATE_LIMIT", "true")
	m.Run()
	os.Unsetenv("DISABLE_RATE_LIMIT")
}

func TestRetrieveRedisClient(t *testing.T) {
	ctx := context.Background()

	t.Run("client found", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Test data
		expectedSM := SerializableMercuryGate{
			Config: &Config{
				UserName: "testuser",
				Password: "testpass",
				Tenant:   "testtenant",
			},
			Cookies: []SerializableCookie{
				{
					Name:    "session",
					Value:   "abc123",
					Path:    "/",
					Domain:  ".mercurygate.com",
					Expires: time.Now().Add(time.Hour),
				},
			},
		}

		jsonBytes, err := json.Marshal(expectedSM)
		require.NoError(t, err)

		mock.ExpectGet("service-1-tms-2-mercurygate").SetVal(string(jsonBytes))

		// Execute
		client := retrieveRedisClient(ctx, 1, 2)

		// Verify
		require.NotNil(t, client)
		assert.Equal(t, expectedSM.Config.UserName, client.config.UserName)
		assert.Equal(t, expectedSM.Config.Password, client.config.Password)
		assert.Equal(t, expectedSM.Config.Tenant, client.config.Tenant)
		assert.Len(t, client.cookies, 1)
		assert.Equal(t, "session", client.cookies[0].Name)
		assert.Equal(t, "abc123", client.cookies[0].Value)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("client not found", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		mock.ExpectGet("service-1-tms-2-mercurygate").RedisNil()

		// Execute
		client := retrieveRedisClient(ctx, 1, 2)

		// Verify
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("redis error", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		mock.ExpectGet("service-1-tms-2-mercurygate").SetErr(assert.AnError)

		// Execute
		client := retrieveRedisClient(ctx, 1, 2)

		// Verify
		// Should not return error for Redis failures
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("invalid json", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		mock.ExpectGet("service-1-tms-2-mercurygate").SetVal("invalid json")

		// Execute
		client := retrieveRedisClient(ctx, 1, 2)

		// Verify
		// Should not return error for JSON unmarshal failures
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCacheClient(t *testing.T) {
	ctx := context.Background()

	t.Run("successful cache", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Create test client
		client := &MercuryGate{
			config: &Config{
				UserName: "testuser",
				Password: "testpass",
				Tenant:   "testtenant",
			},
			cookies: []*http.Cookie{
				{
					Name:    "session",
					Value:   "abc123",
					Path:    "/",
					Domain:  ".mercurygate.com",
					Expires: time.Now().Add(time.Hour),
				},
			},
			tms: models.Integration{ServiceID: 1, Model: gorm.Model{ID: 2}},
		}

		// Expect any JSON value to be set with 3 hour expiration
		mock.Regexp().ExpectSet("service-1-tms-2-mercurygate", `.*`, 3*time.Hour).SetVal("OK")

		// Execute
		client.cacheClient(ctx)

		// Verify
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("redis set error", func(t *testing.T) {
		// Setup mock
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := rediscommon.RDB
		rediscommon.RDB = mockRDB
		defer func() {
			rediscommon.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Create test client
		client := &MercuryGate{
			config: &Config{
				UserName: "testuser",
				Password: "testpass",
				Tenant:   "testtenant",
			},
			cookies: []*http.Cookie{},
			tms:     models.Integration{ServiceID: 1, Model: gorm.Model{ID: 2}},
		}

		// Expect set to fail
		mock.Regexp().ExpectSet("service-1-tms-2-mercurygate", `.*`, 3*time.Hour).SetErr(assert.AnError)

		// Execute (should not panic or return error)
		client.cacheClient(ctx)

		// Verify
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRedisClientKey(t *testing.T) {
	tests := []struct {
		name      string
		serviceID uint
		tmsID     uint
		expected  string
	}{
		{"basic case", 1, 2, "service-1-tms-2-mercurygate"},
		{"zero values", 0, 0, "service-0-tms-0-mercurygate"},
		{"large values", 9999, 8888, "service-9999-tms-8888-mercurygate"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := redisClientKey(tt.serviceID, tt.tmsID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSerializableMercuryGate(t *testing.T) {
	t.Run("json serialization round trip", func(t *testing.T) {
		original := SerializableMercuryGate{
			Config: &Config{
				UserName: "testuser",
				Password: "testpass",
				Tenant:   "testtenant",
			},
			Cookies: []SerializableCookie{
				{
					Name:    "session",
					Value:   "abc123",
					Path:    "/",
					Domain:  ".mercurygate.com",
					Expires: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				},
				{
					Name:    "token",
					Value:   "xyz789",
					Path:    "/api",
					Domain:  "api.mercurygate.com",
					Expires: time.Date(2024, 1, 2, 12, 0, 0, 0, time.UTC),
				},
			},
		}

		// Marshal to JSON
		jsonBytes, err := json.Marshal(original)
		require.NoError(t, err)

		// Unmarshal back
		var restored SerializableMercuryGate
		err = json.Unmarshal(jsonBytes, &restored)
		require.NoError(t, err)

		// Verify
		assert.Equal(t, original.Config.UserName, restored.Config.UserName)
		assert.Equal(t, original.Config.Password, restored.Config.Password)
		assert.Equal(t, original.Config.Tenant, restored.Config.Tenant)
		require.Len(t, restored.Cookies, 2)
		assert.Equal(t, original.Cookies[0].Name, restored.Cookies[0].Name)
		assert.Equal(t, original.Cookies[0].Value, restored.Cookies[0].Value)
		assert.Equal(t, original.Cookies[1].Name, restored.Cookies[1].Name)
		assert.Equal(t, original.Cookies[1].Value, restored.Cookies[1].Value)
	})
}
