package aljex

import (
	"context"
	"errors"
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	commonredis "github.com/drumkitai/drumkit/common/redis"
)

func TestRetrieveRedisClient(t *testing.T) {
	ctx := context.Background()
	serviceID := uint(1)
	tmsID := uint(2)
	redisKey := "service-1-tms-2-aljex"

	db, mock := redismock.NewClientMock()

	originalRDB := commonredis.RDB
	commonredis.RDB = db
	defer func() { commonredis.RDB = originalRDB }()

	t.Run("client found in redis with cookies", func(t *testing.T) {
		sa := SerializableAljex{
			Config: &Config{
				Tenant: "test-tenant",
			},
			Creds: &Credentials{
				Name: "test-user",
			},
			Cookies: []SerializableCookie{
				{
					Name:    "session",
					Value:   "12345",
					Domain:  "example.com",
					Path:    "/",
					Expires: time.Now().Add(1 * time.Hour),
				},
			},
		}

		//nolint:lll
		mock.ExpectGet(redisKey).SetVal(`{"config":{"tenant":"test-tenant"},"creds":{"name":"test-user"},"cookies":[{"name":"session","value":"12345","path":"/","domain":"example.com","expires":"2025-09-16T10:00:00Z"}]}`)

		client := retrieveRedisClient(ctx, serviceID, tmsID)
		require.NotNil(t, client)
		assert.Equal(t, sa.Config.Tenant, client.config.Tenant)
		assert.Equal(t, sa.Creds.Name, client.creds.Name)
		require.Len(t, client.cookies, 1)
		assert.Equal(t, "session", client.cookies[0].Name)
		assert.Equal(t, "12345", client.cookies[0].Value)
		assert.NotNil(t, client.httpClient)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("client found in redis without cookies", func(t *testing.T) {
		mock.ExpectGet(redisKey).SetVal(`{"config":{"tenant":"test-tenant"},"creds":{"name":"test-user"},"cookies":[]}`)

		client := retrieveRedisClient(ctx, serviceID, tmsID)
		require.NotNil(t, client)
		assert.Equal(t, "test-tenant", client.config.Tenant)
		assert.Equal(t, "test-user", client.creds.Name)
		assert.Len(t, client.cookies, 0)
		assert.NotNil(t, client.httpClient)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("client not found in redis", func(t *testing.T) {
		mock.ExpectGet(redisKey).RedisNil()

		client := retrieveRedisClient(ctx, serviceID, tmsID)
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("redis returns an error", func(t *testing.T) {
		mock.ExpectGet(redisKey).SetErr(errors.New("redis error"))

		client := retrieveRedisClient(ctx, serviceID, tmsID)
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("unmarshal error", func(t *testing.T) {
		mock.ExpectGet(redisKey).SetVal("this is not json")

		client := retrieveRedisClient(ctx, serviceID, tmsID)
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCacheClient(t *testing.T) {
	ctx := context.Background()
	redisKey := "service-1-tms-2-aljex"

	db, mock := redismock.NewClientMock()

	originalRDB := commonredis.RDB
	commonredis.RDB = db
	defer func() { commonredis.RDB = originalRDB }()

	t.Run("successfully caches client", func(t *testing.T) {
		client := &Aljex{
			config: &Config{
				Tenant: "test-tenant",
			},
			creds: &Credentials{
				Name: "test-user",
			},
			cookies: []*http.Cookie{
				{
					Name:    "session",
					Value:   "12345",
					Domain:  "example.com",
					Path:    "/",
					Expires: time.Now().Add(1 * time.Hour),
				},
			},
			tms: models.Integration{
				Model:     gorm.Model{ID: 2},
				ServiceID: 1,
			},
		}

		mock.Regexp().ExpectSet(redisKey, `.*`, time.Hour*3).SetVal("OK")

		client.cacheClient(ctx)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
