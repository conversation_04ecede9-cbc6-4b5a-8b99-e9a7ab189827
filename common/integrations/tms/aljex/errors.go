package aljex

import (
	"errors"
	"regexp"
	"strings"

	"github.com/PuerkitoBio/goquery"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
)

// cleanErrorMessage extracts the main error messages from Aljex HTML responses.
// This function parses HTML like the example provided to extract clean "Failed Edit: ..." messages.
func cleanErrorMessage(origErr error) string {

	var httpErr errtypes.HTTPResponseError
	if !errors.As(origErr, &httpErr) {
		return ""
	}

	// Fallback: return the original HTML body if no patterns matched
	htmlBody := string(httpErr.ResponseBody)
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlBody))
	if err != nil {
		return htmlBody
	}

	// Remove script and a tags to avoid extracting JavaScript code
	doc.Find("script").Remove()
	doc.Find("a").Remove()

	res := strings.TrimSpace(doc.Find("b").First().Text())
	if helpers.IsBlank(res) {
		// Try searching for center tags
		res = strings.TrimSpace(doc.Find("center").First().Text())
	}
	// If no bold or center tags, return the original HTML
	if helpers.IsBlank(res) {
		res, err := doc.Html()
		if err != nil {
			return htmlBody
		}

		return res
	}

	res = strings.ReplaceAll(res, "\r", "\n")
	// Remove extra spaces but preserve newlines first
	res = regexp.MustCompile(`[^\S\n]+`).ReplaceAllString(res, " ")
	// Then remove duplicate newlines (2 or more newlines become 1)
	res = regexp.MustCompile(`\n+`).ReplaceAllString(res, "\n")
	// Clean up any remaining whitespace around newlines
	res = regexp.MustCompile(`\s*\n\s*`).ReplaceAllString(res, "\n")

	return res
}
