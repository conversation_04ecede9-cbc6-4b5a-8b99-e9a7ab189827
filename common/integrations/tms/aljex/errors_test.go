package aljex

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/errtypes"
)

func TestCleanAljexErrorMessages(t *testing.T) {
	t.Run("extracts single phone error message", func(t *testing.T) {
		t.<PERSON>llel()

		htmlBody := `<html>
<head>
    <title>Aljex Returned This Message</title>
</head>
<body bgcolor=#c9d7e5>
    <center>
        <div style="width:60%;background:#fff;border:3px solid #eee;border-radius:10px;padding:30px;margin: 100px
            auto" <h2>The Following Message Was Returned:</h2>
            <hr width="60%">
            <p>
                <center>
                    <b>
                        <center>
                            <br>
                            <b>"A7) Pickup Phone #" - Failed Edit: "phone" Using: "(208) 841-02d6"
                                <br>
                                <p>

                                    <center>

                                        <script>
                                            function doclose() {
                                                history.go(-1);
                                                setTimeout("window.close()", 1000);
                                            }
                                        </script>
                                        <a href="javascript:doclose()">BACK</a>
                                        <br>
        </div>
</body>
</html>`
		httpErr := errtypes.HTTPResponseError{
			ResponseBody: []byte(htmlBody),
		}
		result := cleanErrorMessage(httpErr)
		expected := `"A7) Pickup Phone #" - Failed Edit: "phone" Using: "(208) 841-02d6"`
		assert.Equal(t, expected, result)
	})

	t.Run("extracts multiple error messages", func(t *testing.T) {
		t.Parallel()
		htmlBody := `
		<html>

<head>
    <title>Aljex Returned This Message</title>
</head>

<body bgcolor=#c9d7e5>
    <center>
        <div style="width:60%;background:#fff;border:3px solid #eee;border-radius:10px;padding:30px;margin: 100px auto"
            <h2>The Following Message Was Returned:</h2>
            <hr width="60%">
            <p>
                <center>

                    <b>
                        <center>
                            <br>
                            <b>"A7) Pickup Phone #" - Failed Edit: "phone" Using: "(208) 841-02d6"
                                <br>
                                <b>"A7)Consignee Phone" - Failed Edit: "phone" Using: "**************"
                                    <br>
                                    <p>

                                        <center>

                                            <script>
                                                function doclose() {
                                                    history.go(-1);
                                                    setTimeout("window.close()", 1000);
                                                }
                                            </script>
                                            <a href="javascript:doclose()">BACK</a>
                                            <br>
        </div>
</body>

</html>`

		httpErr := errtypes.HTTPResponseError{
			ResponseBody: []byte(htmlBody),
		}
		result := cleanErrorMessage(httpErr)
		expected := `"A7) Pickup Phone #" - Failed Edit: "phone" Using: "(208) 841-02d6"
"A7)Consignee Phone" - Failed Edit: "phone" Using: "**************"` // Alpha chars and country code not allowed
		assert.Equal(t, expected, result)
	})

	t.Run("extracts customer error", func(t *testing.T) {

		htmlBody := `<html>
<head>
<title>Aljex Returned This Message</title>
</head>
<body>
<h2>The Following Message Was Returned:</h2>
<hr>
<center>
<b>"Customer Name" - Failed Edit: "name" Using: "Test Customer"
</center>
</body>
</html>`

		httpErr := errtypes.HTTPResponseError{
			ResponseBody: []byte(htmlBody),
		}
		result := cleanErrorMessage(httpErr)
		expected := `"Customer Name" - Failed Edit: "name" Using: "Test Customer"`
		assert.Equal(t, expected, result)
	})

	t.Run("returns original content when no center tags found", func(t *testing.T) {
		t.Parallel()
		htmlBody := `<html>
<head>
<title>Aljex Returned This Message</title>
</head>
<body>
<h2>The Following Message Was Returned:</h2>
<hr>
<p>Some other error message</p>
</body>
</html>`

		httpErr := errtypes.HTTPResponseError{
			ResponseBody: []byte(htmlBody),
		}
		result := cleanErrorMessage(httpErr)
		// Goquery cleans malformed Aljex HTML
		expectedCleanHTML := `<html><head>
<title>Aljex Returned This Message</title>
</head>
<body>
<h2>The Following Message Was Returned:</h2>
<hr/>
<p>Some other error message</p>

</body></html>`
		// Should return the original HTML since no center tags found
		assert.Equal(t, expectedCleanHTML, result)
	})

	t.Run("extracts content from center tags", func(t *testing.T) {
		t.Parallel()
		htmlBody := `<html>
<body>
<h2>The Following Message Was Returned:</h2>
<hr width="60%">
<center>
Invalid customer data provided
</center>
<p>
</body>
</html>`

		httpErr := errtypes.HTTPResponseError{
			ResponseBody: []byte(htmlBody),
		}
		result := cleanErrorMessage(httpErr)
		expected := "Invalid customer data provided"
		assert.Equal(t, expected, result)
	})
}
