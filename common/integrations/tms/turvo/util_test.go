package turvo

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFindWeightUnitWithFallback(t *testing.T) {
	tests := []struct {
		name           string
		unit           string
		weight         float32
		expectedResult string
		description    string
	}{
		// Zero weight cases
		{
			name:           "zero_weight_returns_empty",
			unit:           "lbs",
			weight:         0,
			expectedResult: "",
			description:    "Should return empty string when weight is zero",
		},
		{
			name:           "negative_weight_returns_empty",
			unit:           "lbs",
			weight:         -10,
			expectedResult: "",
			description:    "Should return empty string when weight is negative",
		},

		// Direct mapping cases (units that exist in WeightUnitReverseMap)
		{
			name:           "direct_mapping_lb",
			unit:           "lb",
			weight:         42000,
			expectedResult: "lb",
			description:    "Should map 'lb' directly from WeightUnitReverseMap",
		},
		{
			name:           "direct_mapping_kg",
			unit:           "kg",
			weight:         19050,
			expectedResult: "g", // FindMatchingKey does fuzzy matching, "kg" contains "g"
			description:    "Should find 'g' via fuzzy matching from WeightUnitReverseMap",
		},
		{
			name:           "direct_mapping_oz",
			unit:           "oz",
			weight:         672000,
			expectedResult: "oz",
			description:    "Should map 'oz' directly from WeightUnitReverseMap",
		},

		// Fallback mapping cases (common variations)
		{
			name:           "fallback_lbs_to_lb",
			unit:           "lbs",
			weight:         42000,
			expectedResult: "lb",
			description:    "Should map 'lbs' to 'lb' via fallback logic",
		},
		{
			name:           "fallback_pounds_to_lb",
			unit:           "pounds",
			weight:         42000,
			expectedResult: "lb",
			description:    "Should map 'pounds' to 'lb' via fallback logic",
		},
		{
			name:           "fallback_pound_to_lb",
			unit:           "pound",
			weight:         42000,
			expectedResult: "lb",
			description:    "Should map 'pound' to 'lb' via fallback logic",
		},
		{
			name:           "fallback_kilograms_to_kg",
			unit:           "kilograms",
			weight:         19050,
			expectedResult: "g", // FindMatchingKey finds "g" first (fuzzy match)
			description:    "Should find 'g' via fuzzy matching from WeightUnitReverseMap",
		},
		{
			name:           "fallback_kilogram_to_kg",
			unit:           "kilogram",
			weight:         19050,
			expectedResult: "g", // FindMatchingKey finds "g" first (fuzzy match)
			description:    "Should find 'g' via fuzzy matching from WeightUnitReverseMap",
		},
		{
			name:           "fallback_kgs_to_kg",
			unit:           "kgs",
			weight:         19050,
			expectedResult: "g", // FindMatchingKey finds "g" first (fuzzy match)
			description:    "Should find 'g' via fuzzy matching from WeightUnitReverseMap",
		},
		{
			name:           "fallback_grams_to_g",
			unit:           "grams",
			weight:         19050000,
			expectedResult: "g",
			description:    "Should map 'grams' to 'g' via fallback logic",
		},
		{
			name:           "fallback_gram_to_g",
			unit:           "gram",
			weight:         19050000,
			expectedResult: "g",
			description:    "Should map 'gram' to 'g' via fallback logic",
		},
		{
			name:           "fallback_tons_to_ton",
			unit:           "tons",
			weight:         21,
			expectedResult: "t", // FindMatchingKey finds "t" first (fuzzy match)
			description:    "Should find 't' via fuzzy matching from WeightUnitReverseMap",
		},
		{
			name:           "fallback_ounces_to_oz",
			unit:           "ounces",
			weight:         672000,
			expectedResult: "oz",
			description:    "Should map 'ounces' to 'oz' via fallback logic",
		},
		{
			name:           "fallback_ounce_to_oz",
			unit:           "ounce",
			weight:         672000,
			expectedResult: "oz",
			description:    "Should map 'ounce' to 'oz' via fallback logic",
		},
		{
			name:           "fallback_tonnes_to_t",
			unit:           "tonnes",
			weight:         19.05,
			expectedResult: "t",
			description:    "Should map 'tonnes' to 't' via fallback logic",
		},
		{
			name:           "fallback_metric_tons_to_t",
			unit:           "metric tons",
			weight:         19.05,
			expectedResult: "t",
			description:    "Should map 'metric tons' to 't' via fallback logic",
		},
		{
			name:           "fallback_metric_ton_to_t",
			unit:           "metric ton",
			weight:         19.05,
			expectedResult: "t",
			description:    "Should map 'metric ton' to 't' via fallback logic",
		},

		// Case insensitive tests
		{
			name:           "case_insensitive_LBS",
			unit:           "LBS",
			weight:         42000,
			expectedResult: "lb",
			description:    "Should handle uppercase 'LBS' and map to 'lb'",
		},
		{
			name:           "case_insensitive_POUNDS",
			unit:           "POUNDS",
			weight:         42000,
			expectedResult: "lb",
			description:    "Should handle uppercase 'POUNDS' and map to 'lb'",
		},
		{
			name:           "case_insensitive_mixed",
			unit:           "KiLoGrAmS",
			weight:         19050,
			expectedResult: "g", // FindMatchingKey finds "g" first (fuzzy match)
			description:    "Should handle mixed case and find 'g' via fuzzy matching",
		},

		// Whitespace handling
		{
			name:           "whitespace_trimming",
			unit:           "  lbs  ",
			weight:         42000,
			expectedResult: "lb",
			description:    "Should trim whitespace and map 'lbs' to 'lb'",
		},

		// Unknown unit cases
		{
			name:           "unknown_unit_with_weight",
			unit:           "xyz",
			weight:         42000,
			expectedResult: "",
			description:    "Should return empty string for unknown units",
		},
		{
			name:           "empty_unit_with_weight",
			unit:           "",
			weight:         42000,
			expectedResult: "",
			description:    "Should return empty string for empty unit",
		},
		{
			name:           "invalid_unit_with_weight",
			unit:           "invalid_unit_123",
			weight:         42000,
			expectedResult: "t", // FindMatchingKey finds "t" because "invalid_unit_123" contains "t"
			description:    "Should find 't' via fuzzy matching even in invalid unit strings",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := findWeightUnitWithFallback(tt.unit, tt.weight)
			assert.Equal(t, tt.expectedResult, result, tt.description)
		})
	}
}
