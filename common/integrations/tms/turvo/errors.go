package turvo

import (
	"encoding/json"
	"errors"
	"strings"

	"github.com/drumkitai/drumkit/common/errtypes"
)

// cleanErrorMessage extracts a user-facing error string from Turvo error responses.
// - If body is a Turvo error JSON with details.errorMessage, returns that message.
// - Else returns the raw response body as a string.
func cleanErrorMessage(origErr error) string {
	var httpErr errtypes.HTTPResponseError
	if !errors.As(origErr, &httpErr) {
		return ""
	}

	body := strings.TrimSpace(string(httpErr.ResponseBody))
	if body == "" {
		return ""
	}

	var turvoErr struct {
		Status  string `json:"status"`
		Details struct {
			ErrorMessage string `json:"errorMessage"`
		} `json:"details"`
	}
	if err := json.Unmarshal(httpErr.ResponseBody, &turvoErr); err == nil {
		if msg := strings.TrimSpace(turvoErr.Details.ErrorMessage); msg != "" {
			return msg
		}
	}

	return body
}
