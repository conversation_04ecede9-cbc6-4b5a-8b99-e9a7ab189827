package mcleodenterprise

import (
	"context"
	"net/url"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/models"
)

func (m *McleodEnterprise) GetUsers(ctx context.Context) (users []models.TMSUser, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetUsersMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	queryParams := url.Values{}
	// NOTE: Users service contains external users as well (carriers, etc) and so we approximate broker's employees
	// via `operations_user_id` attribute
	queryParams.Set("operations_user_id", "*")
	queryParams.Set("is_active", "true")
	queryParams.Set("recordLength", "1000") // Default is 100, <1000 in Trident

	var usersResp []UserResp
	err = m.get(ctx, "/users/search", queryParams, &usersResp, s3backup.TypeUsers)
	if err != nil {
		return nil, err
	}

	for _, data := range usersResp {
		user := models.TMSUser{}
		user.TMSID = m.tms.ID

		user.ExternalTMSID = data.ID // QN how does this change affect Trident?
		user.Username = data.Name
		user.EmailAddress = data.EmailAddress
		user.RevenueCode = data.RevenueCodeID
		user.OperationsUser = data.OperationsUserID

		users = append(users, user)
	}
	return users, nil
}
