package mcleodenterprise

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (m *McleodEnterprise) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "InitialOnBoardMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	if onboardRequest.Username == "" || (onboardRequest.Password == "" && onboardRequest.AccessToken == "") {
		return models.OnboardTMSResponse{},
			errors.New("missing McleodEnterprise API credentials; password or access token is required")
	}

	return m.authenticate(ctx, onboardRequest)
}

func (m *McleodEnterprise) authenticate(
	ctx context.Context,
	req models.OnboardTMSRequest,
) (models.OnboardTMSResponse, error) {
	var accessToken string
	var err error

	if req.Password != "" {
		accessToken, err = m.getTokenViaBasicAuth(ctx, req.Username, req.Password)
	} else {
		accessToken, err = m.getTokenViaToken(ctx, req.AccessToken)
	}
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("getToken failed: %w", err)
	}

	log.Info(ctx, "Successfully authenticated McleodEnterprise client")

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, req.Password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("password encryption failed: %w", err)
	}

	return models.OnboardTMSResponse{
		AccessToken:       accessToken,
		EncryptedPassword: encryptedPassword,
		Username:          req.Username,
		Tenant:            req.Tenant,
	}, err
}

func (m *McleodEnterprise) getTokenViaBasicAuth(
	ctx context.Context,
	username, password string,
) (result string, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "getTokenViaBasicAuthMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	auth := "Basic " + base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", username, password)))
	headers := map[string]string{"Authorization": auth}

	err = m.post(ctx, "/users/login", nil, &result, s3backup.TypeTokens, headers)
	if err != nil {
		return "", fmt.Errorf("POST token failed: %w", err)
	}

	return result, nil
}

func (m *McleodEnterprise) getTokenViaToken(
	ctx context.Context,
	token string,
) (result string, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "getTokenViaTokenMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	headers := map[string]string{"Authorization": "Bearer " + token}

	err = m.post(ctx, "/users/login", nil, &result, s3backup.TypeTokens, headers)
	if err != nil {
		return "", fmt.Errorf("POST token failed: %w", err)
	}

	return result, nil
}
