package mcleodenterprise

import (
	"fmt"
	"strings"
)

func (m *McleodEnterprise) toAllocationCode(allocLabel string) (string, error) {
	tenant := strings.ToLower(m.tms.Tenant)
	switch {
	case strings.Contains(tenant, TenantTrident):
		return m.toTridentAllocationCode(allocLabel)

	case strings.Contains(tenant, TenantFetch):
		return "", nil // N/A

	case strings.Contains(tenant, TenantSyfan):
		return m.toSyfanAllocationCode(allocLabel)

	case strings.Contains(tenant, TenantTumalo):
		return m.toTumaloAllocationCode(allocLabel)

	default:
		return "", m.unknownTenantError("allocation code")
	}
}

// Only difference from RevenueCode is Minneapolis is mapped to MINNEAPO instead of MINNE
func (m *McleodEnterprise) toTridentAllocationCode(allocLabel string) (string, error) {
	allocLabel = strings.ToLower(allocLabel)
	switch allocLabel {

	case "minneapolis", "minne", "minneapo":
		return "MINNEAPO", nil
	}

	return m.toTridentRevenueCode(allocLabel)
}

func (m *McleodEnterprise) toSyfanAllocationCode(allocLabel string) (string, error) {
	allocLabel = strings.TrimSpace(strings.ToLower(allocLabel))
	// Optional
	if allocLabel == "" {
		return "", nil
	}

	if allocationCode, ok := syfanAllocationCodeOptions[allocLabel]; ok {
		return allocationCode, nil
	}
	return "", fmt.Errorf("unsupported allocation code for tenant %s: %s", m.tms.Tenant, allocLabel)
}

func (m *McleodEnterprise) toTumaloAllocationCode(allocLabel string) (string, error) {
	allocLabel = strings.TrimSpace(strings.ToLower(allocLabel))
	if allocLabel == "" {
		return "", nil
	}

	if allocationCode, ok := tumaloAllocationCodeOptions[allocLabel]; ok {
		return allocationCode, nil
	}

	return allocLabel, nil
}

// Map of Syfan allocation  labels to their corresponding codes
var syfanAllocationCodeOptions = map[string]string{
	"account development team 1,":  "ADT1",
	"business development team 1,": "BDT1",
	"bnsf,":                        "BNSF",
	"dedicated,":                   "DED",
	"drayage 1,":                   "DRAY1",
	"dry,":                         "DRY",
	"expedited,":                   "EXP",
	"expedited 1,":                 "EXP1",
}

var tumaloAllocationCodeOptions = map[string]string{
	"EMILY": "TCT - EMILY",
	"NORTH": "TCT - NORTH",
	"SOUTH": "TCT - SOUTH",
}
