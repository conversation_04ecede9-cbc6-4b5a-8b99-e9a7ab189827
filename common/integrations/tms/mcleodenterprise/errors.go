package mcleodenterprise

import (
	"errors"
	"net/http"
	"regexp"

	"github.com/drumkitai/drumkit/common/errtypes"
)

// Helper function to clean the carrier assignment HTTP response error
// Example Short Error: Unable to assign carrier. Carrier [015ELANV] is not qualified for this movement.
// Example Long Error: Nested exception: Unable to clear the carrier from movement [758953].
// [com.tms.common.lib.data.InvalidDataException: An unassignment reason is required.]
//
// (NOTE: We have FE validation to proactively prevent these 2 errors, but in case sth pops up we didn't see in dev, we handle it)
//
//nolint:lll
func cleanCarrierAssignmentError(err error) string {
	var httpErr errtypes.HTTPResponseError
	if errors.As(err, &httpErr) && httpErr.StatusCode == http.StatusNotAcceptable {

		re := regexp.MustCompile(`InvalidDataException: ([^]]+)`)
		strBody := string(httpErr.ResponseBody)
		match := re.FindStringSubmatch(strBody)

		if len(match) > 1 {
			return match[1]
		}

		return strBody

	}

	// If not a 406 error, return empty string. NewUserFacingError will fallback to default string logic.
	return ""
}
