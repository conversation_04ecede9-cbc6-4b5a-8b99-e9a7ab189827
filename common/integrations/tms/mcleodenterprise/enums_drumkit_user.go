package mcleodenterprise

import "strings"

func (m *McleodEnterprise) toDrumkitUser() (string, error) {
	tenant := strings.ToLower(m.tms.Tenant)
	switch {
	case strings.Contains(tenant, TenantTrident):
		return "drumkit", nil

	case strings.Contains(tenant, TenantFetch):
		return "drumkitai", nil

	case strings.Contains(tenant, TenantSyfan):
		return "dkapi", nil

	case strings.Contains(tenant, TenantTumalo):
		return "drumkit", nil

	default:
		return "", m.unknown<PERSON><PERSON>t<PERSON><PERSON>r("drumkit user")
	}
}
