package mcleodenterprise

import (
	"fmt"
	"regexp"
	"strings"
)

func (m *McleodEnterprise) toOrderType(mode, orderType string) (orderTypeID string, err error) {
	tenant := strings.ToLower(m.tms.Tenant)
	switch {

	case strings.Contains(tenant, TenantTrident):
		// For Trident, mode is essentially the same as Order Type
		return m.toTridentOrderType(mode, orderType)

	case strings.Contains(tenant, TenantFetch):
		// Fetch lets <PERSON><PERSON>Leod autopopulate their order types when load submitted
		return "", nil

	case strings.Contains(tenant, TenantSyfan):
		return m.toSyfanOrderType(mode, orderType)

	case strings.Contains(tenant, TenantTumalo):
		return m.toTumaloOrderType(mode, orderType)

	default:
		return "", m.unknownTenantError("order type")
	}
}

// <PERSON><PERSON>od has both Order Mode (Truckload, Partial) and Order Type (Truckload, LTL, Van, Drayage, Air, etc) ¯\_(ツ)_/¯
// Since we support only FTL/LTL shipments, we can deduce OrderTypeID from Order Mode for Trident as the enums
// are similar.
// As of 2025-08-07, Order Type input is shown in FE form for Trident with support for ENT and T order types.
func (m *McleodEnterprise) toTridentOrderType(mode, orderType string) (orderTypeID string, err error) {
	mode = strings.ToLower(mode)
	orderType = strings.ToLower(orderType)

	// Supporting ENT order type for now (can add other types as Trident starts using load building for other cases)
	if orderType == "ent" {
		return "ENT", nil
	}

	switch mode {
	case "truckload", "tl", "ftl", "t":
		return "TL", nil
	case "ltl", "partial":
		return "LTL", nil
	}

	return "", fmt.Errorf("unexpected order type for tenant %s: %s", m.tms.Tenant, mode)
}

func (m *McleodEnterprise) toSyfanOrderType(_, orderType string) (orderTypeID string, err error) {
	orderType = strings.TrimSpace(strings.ToLower(orderType))
	if orderType == "" {
		return "", nil
	}

	if orderTypeID, ok := syfanOrderLabelToCode[orderType]; ok {
		return orderTypeID, nil
	}

	if _, ok := syfanOrderCodeToLabel[orderType]; ok {
		return strings.ToUpper(orderType), nil
	}

	// Check if input is label without code prepended, workaround for duplicate labels with different codes
	re := regexp.MustCompile(`\s+\-\s+`)
	for label, code := range syfanOrderLabelToCode {
		splits := re.Split(label, 2)
		if len(splits) > 1 {
			if strings.EqualFold(strings.TrimSpace(splits[1]), orderType) {
				return strings.ToUpper(code), nil
			}
		}
	}

	return "", fmt.Errorf("unexpected order type: %s", orderType)
}

func (m *McleodEnterprise) toTumaloOrderType(_, orderType string) (orderTypeID string, err error) {
	// orderType = strings.TrimSpace(strings.ToLower(orderType))
	if orderType == "" {
		return "", nil
	}

	if orderTypeID, ok := tumaloOrderLabelToCode[orderType]; ok {
		return orderTypeID, nil
	}

	_, ok := tumaloOrderCodeToLabel[orderType]
	if ok {
		return strings.ToUpper(orderType), nil
	}

	return "", fmt.Errorf("unexpected order type: %s", orderType)
}

var tumaloOrderLabelToCode = map[string]string{
	"air - air freight shipment":              "AIR",
	"avaiahot - available to cover-hot load":  "AVAIAHOT",
	"availabl - available to cover":           "AVAILABL",
	"availhaz - available to cover-hazmat":    "AVAILHAZ",
	"availoff - available to cover-offered l": "AVAILOFF",
	"availptl - available to cover - partial": "AVAILPTL",
	"caution - don't forget about this load":  "CAUTION",
	"claim - claim pending":                   "CLAIM",
	"commit - these loads are accepted":       "COMMIT",
	"containe - container freight":            "CONTAINE",
	"covered - carrier assigned":              "COVERED",
	"detappr - waiting on detention approve":  "DETAPPR",
	"finalchk - final appt check":             "FINALCHK",
	"flaa - florida air":                      "FLAA",
	"flal - florida ltl":                      "FLAL",
	"flao - florida other":                    "FLAO",
	"flap - florida parcel":                   "FLAP",
	"flat - florida truck":                    "FLAT",
	"freezpro - waiting for freeze protectio": "FREEZPRO",
	"imdl - intermodal shipment":              "IMDL",
	"info - awaiting order information":       "INFO",
	"issue - load with issues":                "ISSUE",
	"late - load is late":                     "LATE",
	"ltl - less than truckload":               "LTL",
	"needappt - an appointment is needed":     "NEEDAPPT",
	"onhold - order is on hold":               "ONHOLD",
	"pod - waiting on pod":                    "POD",
	"rate - looking for rate information":     "RATE",
	"reworkld - tipped / shifted load":        "REWORKLD",
	"roll - rolling booking":                  "ROLL",
	"spot - spot market loads.":               "SPOT",
	"spotadj - waiting-spot pricing conf.":    "SPOTADJ",
	"storage - drop trailer - storage":        "STORAGE",
	"tonu - truck ordered not used":           "TONU",
	"twic - requires a government twic":       "TWIC",
	"waitinfo - waiting on information":       "WAITINFO",
	"warehous - warehousing fees":             "WAREHOUS",
	"wtnasses - waiting on assessorial charg": "WTNASSES",
}

var tumaloOrderCodeToLabel = map[string]string{
	"AIR":      "air - air freight shipment",
	"AVAIAHOT": "avaiahot - available to cover-hot load",
	"AVAILABL": "availabl - available to cover",
	"AVAILHAZ": "availhaz - available to cover-hazmat",
	"AVAILOFF": "availoff - available to cover-offered l",
	"AVAILPTL": "availptl - available to cover - partial",
	"CAUTION":  "caution - don't forget about this load",
	"CLAIM":    "claim - claim pending",
	"COMMIT":   "commit - these loads are accepted",
	"CONTAINE": "containe - container freight",
	"COVERED":  "covered - carrier assigned",
	"DETAPPR":  "detappr - waiting on detention approve",
	"FINALCHK": "finalchk - final appt check",
	"FLAA":     "flaa - florida air",
	"FLAL":     "flal - florida ltl",
	"FLAO":     "flao - florida other",
	"FLAP":     "flap - florida parcel",
	"FLAT":     "flat - florida truck",
	"FREEZPRO": "freezpro - waiting for freeze protectio",
	"IMDL":     "imdl - intermodal shipment",
	"INFO":     "info - awaiting order information",
	"ISSUE":    "issue - load with issues",
	"LATE":     "late - load is late",
	"LTL":      "ltl - less than truckload",
	"NEEDAPPT": "needappt - an appointment is needed",
	"ONHOLD":   "onhold - order is on hold",
	"POD":      "pod - waiting on pod",
	"RATE":     "rate - looking for rate information",
	"REWORKLD": "reworkld - tipped / shifted load",
	"ROLL":     "roll - rolling booking",
	"SPOT":     "spot - spot market loads.",
	"SPOTADJ":  "spotadj - waiting-spot pricing conf.",
	"STORAGE":  "storage - drop trailer - storage",
	"TONU":     "tonu - truck ordered not used",
	"TWIC":     "twic - requires a government twic",
	"WAITINFO": "waitinfo - waiting on information",
	"WAREHOUS": "warehous - warehousing fees",
	"WTNASSES": "wtnasses - waiting on assessorial charg",
}

// Map of Syfan order type labels to their corresponding codes
// NOTE: Because of duplicate labels with different *codes*, we prepend the code to the label here and in FE.
var syfanOrderLabelToCode = map[string]string{
	"3mload - 3m loads":                       "3MLOAD",
	"abbott - abbott nutrition":               "ABBOTT",
	"abicafsc - abi cali fuel":                "ABICAFSC",
	"abireef - anheuser busch reefer load":    "ABIREEF",
	"ace - ace hardware":                      "ACE",
	"actaero - active aero shipments":         "ACTAERO",
	"actcan - actaero canada":                 "ACTCAN",
	"actimex - active aero mexico shipments":  "ACTIMEX",
	"aerotemp - active aero temp controlled":  "AEROTEMP",
	"agildrop - agile cold export drop":       "AGILDROP",
	"agilecol - agile cold storage exports":   "AGILECOL",
	"agillive - agile cold  exports live":     "AGILLIVE",
	"agilmaco - agile macon":                  "AGILMACO",
	"agilproj - agile special project":        "AGILPROJ",
	"agrosup - agrosuper":                     "AGROSUP",
	"ainswrth - transplace - ainsworth":       "AINSWRTH",
	"airexp - ups air team load":              "AIREXP",
	"airexs - ups air solo load":              "AIREXS",
	"akpizza - port city bakery dba ak pizz":  "AKPIZZA",
	"albaf - alba form inc":                   "ALBAF",
	"alliriwi - alliance laundry":             "ALLIRIWI",
	"allsbuga - allsouth sprikler":            "ALLSBUGA",
	"allsourc - allsource supply inc":         "ALLSOURC",
	"amazon - amazon (direct shipments)":      "AMAZON",
	"amazonga - amazon project":               "AMAZONGA",
	"ambient - ambient loads":                 "AMBIENT",
	"amercolr - american color":               "AMERCOLR",
	"amerdega - ameripipe":                    "AMERDEGA",
	"americol - americold":                    "AMERICOL",
	"amick - amick farms":                     "AMICK",
	"amksolo - amick farms solo":              "AMKSOLO",
	"amkteam - amick farms team":              "AMKTEAM",
	"amrelay - amazon relay":                  "AMRELAY",
	"anhbsch - anheuser busch":                "ANHBSCH",
	"anhebusc - anheuser busch":               "ANHEBUSC",
	"anncmt - announcment convertors":         "ANNCMT",
	"aod-gm - active aero- gm":                "AOD-GM",
	"apex - apex logistics":                   "APEX",
	"aplhot - apl hot shipments":              "APLHOT",
	"apllog - apl logistics":                  "APLLOG",
	"apn - apnonweiler":                       "APN",
	"aprile - aprile":                         "APRILE",
	"aps - equipment":                         "APS",
	"arcbest - arcbest loads":                 "ARCBEST",
	"armada - armada shipment":                "ARMADA",
	"armadhot - armada - hot shipment":        "ARMADHOT",
	"armateam - armada - team shipment":       "ARMATEAM",
	"arrow - arrowstream":                     "ARROW",
	"athens - athens stone casting":           "ATHENS",
	"autocar - yarddog":                       "AUTOCAR",
	"availabl - available load - booked sub":  "AVAILABL",
	"averitt - glass":                         "AVERITT",
	"ax-empty - axis empty trailer moves":     "AX-EMPTY",
	"axis - axis warehouse moves":             "AXIS",
	"axis-nor - axis norfol":                  "AXIS-NOR",
	"azmachin - az machine":                   "AZMACHIN",
	"backup - back up truck":                  "BACKUP",
	"beasley - beasley freight":               "BEASLEY",
	"berk - berk enterprises inc":             "BERK",
	"big4 - auto lubrucation":                 "BIG4",
	"binder - binderholz":                     "BINDER",
	"bitzer - bitzer us, inc":                 "BITZER",
	"blind - blind shipment":                  "BLIND",
	"blindhot - blind shipment hot load":      "BLINDHOT",
	"blindplt - blind shipmt/pallet exchange": "BLINDPLT",
	"blocks - drink blocks llc":               "BLOCKS",
	"bluebird - blue bird body company":       "BLUEBIRD",
	"bluebuff - blue buffalo":                 "BLUEBUFF",
	"bluewat - blue water":                    "BLUEWAT",
	"bndray - bnsf dray moves":                "BNDRAY",
	"bnsf - bnsf rail recovery":               "BNSF",
	"bnsf-hot - bnsf hot loads":               "BNSF-HOT",
	"bnsf-mw - bnsf cicero":                   "BNSF-MW",
	"bnsfdbls - bnsf doubles":                 "BNSFDBLS",
	"bnsfhaz - bnsf hazmat":                   "BNSFHAZ",
	"bnsflog - bnsf logistics":                "BNSFLOG",
	"bnsfotr - bnsf rail recovery":            "BNSFOTR",
	"bnsfpup - pup trailer":                   "BNSFPUP",
	"bodyarmr - bodyarmour":                   "BODYARMR",
	"boone - boone trailer moves":             "BOONE",
	"boonelog - boone logistics":              "BOONELOG",
	"boonepln - boone planned loads":          "BOONEPLN",
	"borg - borg warner":                      "BORG",
	"bourbros - bourbon brothers":             "BOURBROS",
	"brand - brand energy":                    "BRAND",
	"bright - bright farms":                   "BRIGHT",
	"buitoni - buitoni foods loads":           "BUITONI",
	"bulk - bulk head required":               "BULK",
	"burtmd - burtonsville, md":               "BURTMD",
	"bycowi - byco":                           "BYCOWI",
	"cabworld - cab worldwide":                "CABWORLD",
	"camco - camco":                           "CAMCO",
	"capital - capital lighting":              "CAPITAL",
	"careline - careline inc":                 "CARELINE",
	"carg-hot - cargill - hot shipment":       "CARG-HOT",
	"cargill - cargill shipments":             "CARGILL",
	"cargteam - cargill - team shipment":      "CARGTEAM",
	"carlex - carlex -  auto glass":           "CARLEX",
	"cason - cason":                           "CASON",
	"catrplr - caterpillar loads":             "CATRPLR",
	"cbears - charlie bears":                  "CBEARS",
	"cbtrans - cb transportation":             "CBTRANS",
	"cdc - cdc":                               "CDC",
	"cdctemp - cdc temp control":              "CDCTEMP",
	"cejoes - travelmation":                   "CEJOES",
	"celeb - celeb luxury":                    "CELEB",
	"cenatl - central atlantic":               "CENATL",
	"ceva - ceva logistics":                   "CEVA",
	"chainlnk - chainlink":                    "CHAINLNK",
	"chassis - chassis leg":                   "CHASSIS",
	"chelmma - chelmsford, ma":                "CHELMMA",
	"chemtrea - chemtreat":                    "CHEMTREA",
	"chflower - ch robinson flowers":          "CHFLOWER",
	"chicago - chicago,il":                    "CHICAGO",
	"chilled - chilled loads":                 "CHILLED",
	"chipotle - chipotle loads":               "CHIPOTLE",
	"chrysler - chrysler automotive":          "CHRYSLER",
	"coilp+ - coilplus":                       "COILP+",
	"coke - coca-cola":                        "COKE",
	"colonial - colonial materials":           "COLONIAL",
	"conagra - conagra":                       "CONAGRA",
	"conwadd - conway - addhock":              "CONWADD",
	"conway - conway/xpo":                     "CONWAY",
	"copper - poultry broker":                 "COPPER",
	"corrim - corrim doors":                   "CORRIM",
	"coyopeak - coyote trl moves":             "COYOPEAK",
	"coyote - coyote loads":                   "COYOTE",
	"crane - crane solutions":                 "CRANE",
	"crescent - crescent kayaks":              "CRESCENT",
	"critical - failure not an option":        "CRITICAL",
	"crysfeed - crystal feed loads":           "CRYSFEED",
	"csm-hot - csm - hot shipment":            "CSM-HOT",
	"csm-team - csm - team shipment":          "CSM-TEAM",
	"csmbake - csm bakery shipment":           "CSMBAKE",
	"csx - csx service recovery":              "CSX",
	"csx-hot - csx - hot loads":               "CSX-HOT",
	"csx-shut - csx shuttle moves":            "CSX-SHUT",
	"csxchass - csx w/ chassis":               "CSXCHASS",
	"csxd2d - csx door to door":               "CSXD2D",
	"csxlocal - csx local":                    "CSXLOCAL",
	"csxpup - csx pup trailer":                "CSXPUP",
	"ctvtrans - ctv transportation":           "CTVTRANS",
	"danone - danone":                         "DANONE",
	"dart - dart containers":                  "DART",
	"dblblind - double blind":                 "DBLBLIND",
	"dblsair - ups doubles air load":          "DBLSAIR",
	"deepchil - deep chill 28 degrees":        "DEEPCHIL",
	"deepfrz - deep freeze -20":               "DEEPFRZ",
	"deepsout - terminal tractors":            "DEEPSOUT",
	"delmonte - del monte loads":              "DELMONTE",
	"delta - delta cargo load":                "DELTA",
	"detent - detention":                      "DETENT",
	"dhl - dhl global forwarding":             "DHL",
	"dillard - dillards gulf coast":           "DILLARD",
	"docklev - dock levelers":                 "DOCKLEV",
	"dod - dept of defense":                   "DOD",
	"dole - dole":                             "DOLE",
	"donation - donation load":                "DONATION",
	"double - doubles qualified":              "DOUBLE",
	"drilchm - drillchem":                     "DRILCHM",
	"drink - drink blocks llc":                "DRINK",
	"drop - georgia drop trailer project":     "DROP",
	"drop-amb - nestle syfk - ambient":        "DROP-AMB",
	"drop-chl - nestle syfk - chilled":        "DROP-CHL",
	"drop-frz - nestle syfk - frozen":         "DROP-FRZ",
	"drop-tmp - nestle syfk- temp controlled": "DROP-TMP",
	"droptrl - drop trailer":                  "DROPTRL",
	"drpepper - dr.pepper/7up loads":          "DRPEPPER",
	"dry - dry freight":                       "DRY",
	"dsi - distribution services intern":      "DSI",
	"duke - energy company":                   "DUKE",
	"eapolfo - eastern poultry foods":         "EAPOLFO",
	"eastcst - padd1:east coast - fuel":       "EASTCST",
	"echo - echo":                             "ECHO",
	"edi - edi - load updated via edi":        "EDI",
	"edi blin - edi load blind shipment":      "EDI BLIN",
	"edi hot - edi updated hot load via edi":  "EDI HOT",
	"edi plts - edi load pallet xchang":       "EDI PLTS",
	"elkay - elkay interior systems":          "ELKAY",
	"elliott - richard a elliott trans":       "ELLIOTT",
	"emmaus - emmaus foods":                   "EMMAUS",
	"ennis-pa - ennis paint":                  "ENNIS-PA",
	"enru - enru":                             "ENRU",
	"eptusa - eptusa":                         "EPTUSA",
	"eskimo - eskimo cold storage":            "ESKIMO",
	"exp - ups expedited team":                "EXP",
	"exp-pup - ups expedited team pup":        "EXP-PUP",
	"expadhoc - exp adhoc loads":              "EXPADHOC",
	"expair - ups expedited air team":         "EXPAIR",
	"expdbl - exp doubles":                    "EXPDBL",
	"expmeds - ups express crit. pharm.":      "EXPMEDS",
	"expmilt - ups express crit. military":    "EXPMILT",
	"exprail - ups team rail":                 "EXPRAIL",
	"exs - ups expedited solo":                "EXS",
	"exs-pup - ups expedited solo pup":        "EXS-PUP",
	"exsadhoc - exs adhoc loads":              "EXSADHOC",
	"exsair - ups expedited air solo":         "EXSAIR",
	"exsdbl - exs doubles":                    "EXSDBL",
	"exsrail - solo rail":                     "EXSRAIL",
	"farmer - farmer focus":                   "FARMER",
	"fca - fiat chrysler automotive":          "FCA",
	"fcadrop - fca drop and hook":             "FCADROP",
	"fcarail - fca rail moves":                "FCARAIL",
	"fdxadhoc - fedex frt adhoc":              "FDXADHOC",
	"fdxdbl - fedex doubles":                  "FDXDBL",
	"fdxshift - fedex express yard shifters":  "FDXSHIFT",
	"fecrail - florida e.coast railway":       "FECRAIL",
	"fedadhoc - fedex adhoc loads":            "FEDADHOC",
	"fedex - fedex ground loads":              "FEDEX",
	"fedexatl - atl fed exp local":            "FEDEXATL",
	"fedexcc - fedex custom critical":         "FEDEXCC",
	"fedexded - fedex freight dedicated":      "FEDEXDED",
	"fedexhub - fedex hub moves":              "FEDEXHUB",
	"fedexprs - fedex express":                "FEDEXPRS",
	"fedextb - fedex truckload brokerage":     "FEDEXTB",
	"fedfrt - fedex freight":                  "FEDFRT",
	"fedrop - fedex drop":                     "FEDROP",
	"fedstand - fedex standby team":           "FEDSTAND",
	"fedxtemp - fedex temp control":           "FEDXTEMP",
	"fema - fema":                             "FEMA",
	"fenix - fenix parts-gainesville":         "FENIX",
	"ferrcand - ferrara candy":                "FERRCAND",
	"fieldale - fieldale":                     "FIELDALE",
	"finlays - finlays loads":                 "FINLAYS",
	"fitco - fitco loads":                     "FITCO",
	"flatbed - flatbed load":                  "FLATBED",
	"fldturf - field turf":                    "FLDTURF",
	"flexfilm - flex film":                    "FLEXFILM",
	"flexngat - flex n gate":                  "FLEXNGAT",
	"flexport - flexport dba deliverr":        "FLEXPORT",
	"flowerwd - flowerwood":                   "FLOWERWD",
	"flyfresh - flying fresh usa":             "FLYFRESH",
	"foodlion - food lion loads":              "FOODLION",
	"foodtran - foodintransit":                "FOODTRAN",
	"ford250 - ford fsc 1-250 miles":          "FORD250",
	"ford500 - ford fsc 250-500 miles":        "FORD500",
	"ford501 - ford fsc 500+ miles":           "FORD501",
	"forddrop - ford drop and hook":           "FORDDROP",
	"formex - formex":                         "FORMEX",
	"frozen - frozen loads":                   "FROZEN",
	"frthawk - freighthawk loads":             "FRTHAWK",
	"fscauto - fcsautoparts":                  "FSCAUTO",
	"fusionf - fusion furniture, inc":         "FUSIONF",
	"garimark - garimark":                     "GARIMARK",
	"gchda100 - honda gulf 0-100 mi":          "GCHDA100",
	"gchda300 - honda gulf 101-300 mi":        "GCHDA300",
	"gchda800 - honda gulf 301-800 mi":        "GCHDA800",
	"gchda801 - honda gulf 800+ mi":           "GCHDA801",
	"ge - ge appliances":                      "GE",
	"genmills - general mills":                "GENMILLS",
	"geracks - rack return":                   "GERACKS",
	"gerdau - gerdau loads":                   "GERDAU",
	"geveko - thermoplastic sheets":           "GEVEKO",
	"gldcreek - gold creek foods":             "GLDCREEK",
	"globex - globex international inc":       "GLOBEX",
	"globxotr - globex international inc otr": "GLOBXOTR",
	"gm parts - gm parts":                     "GM PARTS",
	"gm-can - gm canada":                      "GM-CAN",
	"gm-cca - gm rxo loads":                   "GM-CCA",
	"gm-dn - gm drop new":                     "GM-DN",
	"gm-drop - gm drop & hook":                "GM-DROP",
	"gm-dun - gm empty racks":                 "GM-DUN",
	"gm-hot - gm hot loads":                   "GM-HOT",
	"gm-mex - mexico shipments":               "GM-MEX",
	"gm-misc - gm misc - not tracked in lms":  "GM-MISC",
	"gm-mxdrp - gm mexico drop & hook loads":  "GM-MXDRP",
	"gm-pn - gm parts new":                    "GM-PN",
	"gm-racks - gm rack load":                 "GM-RACKS",
	"gm-share - gm-share the spare":           "GM-SHARE",
	"gm-tank - tanker loads":                  "GM-TANK",
	"gm-wknd - gm weekend production shipme":  "GM-WKND",
	"gmcca - xpo/gmcca":                       "GMCCA",
	"gmcca-mx - gmcca/menlo - mexico":         "GMCCA-MX",
	"gmhazmat - car parts- hazardous":         "GMHAZMAT",
	"gmptemp - gm parts temp":                 "GMPTEMP",
	"godawgs - uga shipments":                 "GODAWGS",
	"gorfoods - gordon food loads":            "GORFOODS",
	"grap-hot - graphic pkg - hot shipment":   "GRAP-HOT",
	"graphic - graphic packaging":             "GRAPHIC",
	"grapteam - graphic team load":            "GRAPTEAM",
	"greennc - greensboro,nc":                 "GREENNC",
	"grove - grove services":                  "GROVE",
	"groveotr - grove services otr":           "GROVEOTR",
	"gtr - gateway truck and refrigerat":      "GTR",
	"gulfcst - padd3: gulf coast fuel reg":    "GULFCST",
	"gulfcstf - gulf coast":                   "GULFCSTF",
	"gulfsout - gulf south forest products":   "GULFSOUT",
	"hamilton - hamilton group":               "HAMILTON",
	"happtafl - happy's home centers inc":     "HAPPTAFL",
	"hazbatt - atl motive power shipments":    "HAZBATT",
	"hazmat - hazardous materials":            "HAZMAT",
	"hecny - hecny transportation":            "HECNY",
	"hermor - hermiston, or":                  "HERMOR",
	"hfc-part - hospitality freight- partial": "HFC-PART",
	"hhl - hormel jot hhl loads":              "HHL",
	"hmltmgrp - hamilton group llc.":          "HMLTMGRP",
	"hna-rack - honda rack loads":             "HNA-RACK",
	"hogan - hogan":                           "HOGAN",
	"homedept - home depot loads":             "HOMEDEPT",
	"honda - honda expedites":                 "HONDA",
	"honda-ad - honda adhoc":                  "HONDA-AD",
	"honda100 - honda 0-100 miles":            "HONDA100",
	"honda300 - honda 101-300 miles":          "HONDA300",
	"honda800 - honda 301-800 miles":          "HONDA800",
	"honda801 - honda over 801 miles":         "HONDA801",
	"honebear - hone bear":                    "HONEBEAR",
	"hordairy - horizon dairy / uber freight": "HORDAIRY",
	"horizon - horizon air freight":           "HORIZON",
	"hormel - hormel foods":                   "HORMEL",
	"hormelr - hormel reefer":                 "HORMELR",
	"hormelv - hormel dry":                    "HORMELV",
	"horsham - horsham,pa":                    "HORSHAM",
	"hot - hot load":                          "HOT",
	"hot plts - hot load pallet exchange":     "HOT PLTS",
	"huber - huber":                           "HUBER",
	"hubfrt - hubgroup adhoc freight":         "HUBFRT",
	"hubgroup - hub group":                    "HUBGROUP",
	"hubharb - harbor freight":                "HUBHARB",
	"hvac - 1 clear choice hvac":              "HVAC",
	"icecream - ice cream -20f":               "ICECREAM",
	"igs - integratedglobal":                  "IGS",
	"indigo - indigo energy":                  "INDIGO",
	"ingles - ingles":                         "INGLES",
	"intebeoh - packaging":                    "INTEBEOH",
	"interra - interra international":         "INTERRA",
	"intervis - intervision foods":            "INTERVIS",
	"ironhrs - iron horse austions":           "IRONHRS",
	"jaxfl - jacksonville,fl":                 "JAXFL",
	"jbfood - jb group":                       "JBFOOD",
	"jbmulti - j&b multi-stop":                "JBMULTI",
	"jbstate - j&b state-state":               "JBSTATE",
	"jenni-o - jenni-o / hormel":              "JENNI-O",
	"jkylbrwg - jekyll brewing":               "JKYLBRWG",
	"johndeer - john deer":                    "JOHNDEER",
	"johnlarm - john l armitage & co":         "JOHNLARM",
	"joyce - joyce-farms":                     "JOYCE",
	"kenworth - auto car kenworth":            "KENWORTH",
	"khazhot - kubota hazmat hot":             "KHAZHOT",
	"kinexo - kinexo":                         "KINEXO",
	"kinexor - kinexo reefer":                 "KINEXOR",
	"kinexov - kinexo van":                    "KINEXOV",
	"kingint - king's international":          "KINGINT",
	"kirch - kirchoff":                        "KIRCH",
	"kirsch - kirsch transportation":          "KIRSCH",
	"koch - koch foods":                       "KOCH",
	"kochdray - koch foods export dray":       "KOCHDRAY",
	"korman - korman":                         "KORMAN",
	"kraft - kraft foods":                     "KRAFT",
	"kraftdry - kraft dry loads":              "KRAFTDRY",
	"kt-strap - kubota straps required":       "KT-STRAP",
	"kubdire - kubota direct hot del":         "KUBDIRE",
	"kubhaz - kubota hazmat":                  "KUBHAZ",
	"kubhazho - kubota hazmat hot":            "KUBHAZHO",
	"kubhot - kubota hot":                     "KUBHOT",
	"kubota - kubota east":                    "KUBOTA",
	"kubwest - kubota west cali imports":      "KUBWEST",
	"kubwhot - kubota west hot":               "KUBWHOT",
	"kuehne - kuehne + nagel":                 "KUEHNE",
	"lahda100 - honda lowatl 0-100 mi":        "LAHDA100",
	"lahda300 - honda lowatl 101-300 mi":      "LAHDA300",
	"lahda800 - honda lowatl 301-800 mi":      "LAHDA800",
	"lahda801 - honda lowatl 800+ mi":         "LAHDA801",
	"lakanto - lakanto":                       "LAKANTO",
	"lake - lake foods":                       "LAKE",
	"lamex - lamex foods":                     "LAMEX",
	"land - land o' lakes":                    "LAND",
	"langston - langston bags":                "LANGSTON",
	"layover - layover":                       "LAYOVER",
	"lcd - local lcd":                         "LCD",
	"lcdshift - local shifter work":           "LCDSHIFT",
	"lcl - local lcl":                         "LCL",
	"lcldbl - local doubles required":         "LCLDBL",
	"lclpup - local pup":                      "LCLPUP",
	"lifeway - paper products":                "LIFEWAY",
	"lineage - lineage freight management l":  "LINEAGE",
	"linecarg - line cargo imports (kubota)":  "LINECARG",
	"loadlcks - load locks req":               "LOADLCKS",
	"locallcl - local cl":                     "LOCALLCL",
	"loupford - loup ford":                    "LOUPFORD",
	"loupgm - loup gm loads":                  "LOUPGM",
	"louphaz - loup haz shipments":            "LOUPHAZ",
	"louphot - loup hot shipments":            "LOUPHOT",
	"louppup - loup pup trailer":              "LOUPPUP",
	"louprail - loup rail (up rail)":          "LOUPRAIL",
	"loupsvcr - loup service recovery":        "LOUPSVCR",
	"louptsla - loup tesla":                   "LOUPTSLA",
	"loupwhol - loup wholesale loads":         "LOUPWHOL",
	"lowatl - lower atlantic":                 "LOWATL",
	"lscs - landstar supply chain":            "LSCS",
	"ltl - ltl loads":                         "LTL",
	"machine - machinery":                     "MACHINE",
	"maersk - maersk, inc":                    "MAERSK",
	"manwah - manwah furniture":               "MANWAH",
	"marine - marine-specialties":             "MARINE",
	"marubeni - marubeni trans":               "MARUBENI",
	"mauserpk - mauser packaging solutions":   "MAUSERPK",
	"mccain - mccain":                         "MCCAIN",
	"mcgriff - mcgriff treading":              "MCGRIFF",
	"mcmaster - mcmaster-carr":                "MCMASTER",
	"mec-air - morrison air load":             "MEC-AIR",
	"mec-appl - morrison express apple":       "MEC-APPL",
	"mec-bas - morrison - briggs & stratton":  "MEC-BAS",
	"mec-mas - morrison - masonite":           "MEC-MAS",
	"mem-haz - memphis bnsf project hazmat":   "MEM-HAZ",
	"mem-proj - bnsf memphis project":         "MEM-PROJ",
	"memphis - memphis project":               "MEMPHIS",
	"men-team - menlo team":                   "MEN-TEAM",
	"menasha - menasha":                       "MENASHA",
	"menlo - menlo worldwide":                 "MENLO",
	"meritor - meritor parts shipments":       "MERITOR",
	"midwest - bnsf il mn":                    "MIDWEST",
	"midwestf - padd2: midwest fuel region":   "MIDWESTF",
	"milecorp - mile corp trailer project":    "MILECORP",
	"mincey - mincey marble loads":            "MINCEY",
	"minn - minneapolis, mn":                  "MINN",
	"misc - miscellaneous":                    "MISC",
	"missoula - missoula, mt":                 "MISSOULA",
	"mnx - mnx ups":                           "MNX",
	"mondi - mondi group":                     "MONDI",
	"montal - montgomery,al":                  "MONTAL",
	"montile - monterrey tile":                "MONTILE",
	"morrison - morrison express":             "MORRISON",
	"morsolo - morrison express corp solo":    "MORSOLO",
	"morteam - morrison express corp team":    "MORTEAM",
	"mps - paper product":                     "MPS",
	"mri - manufacturing resources intl":      "MRI",
	"murray - murray plastics":                "MURRAY",
	"n.bergen - north bergen,nj":              "N.BERGEN",
	"nacarato - truck center":                 "NACARATO",
	"nahda100 - honda nat'avg 0-100 mi":       "NAHDA100",
	"nahda300 - honda nat'avg 101-300 mi":     "NAHDA300",
	"nahda800 - honda nat'avg 301-800 mi":     "NAHDA800",
	"nahda801 - honda nat'avg 800+ mi":        "NAHDA801",
	"nak - nal kiln services llc":             "NAK",
	"new cust - menasha":                      "NEW CUST",
	"newcold - new cold shuttle program":      "NEWCOLD",
	"newcus - new customer":                   "NEWCUS",
	"newefood - new england foods":            "NEWEFOOD",
	"nextlog - nextlogistix":                  "NEXTLOG",
	"nfi - nfi industries":                    "NFI",
	"nhstemp - nestle nhs temp controlled":    "NHSTEMP",
	"nri - nri":                               "NRI",
	"nshazmat - ns rail hazmat":               "NSHAZMAT",
	"nsheavy - ns loads 43000+ lbs":           "NSHEAVY",
	"nshot - ns rail hot load":                "NSHOT",
	"nspup - ns rail pup load":                "NSPUP",
	"nsrail - norfolk southern recovery":      "NSRAIL",
	"nucor - nucor steel kingman":             "NUCOR",
	"oasis - oasis consulting services":       "OASIS",
	"oldcast - old castle":                    "OLDCAST",
	"orgameti - orgametics llc":               "ORGAMETI",
	"ovainn - ovain novations":                "OVAINN",
	"overdim - overdimensional":               "OVERDIM",
	"p-flower - publix flowers":               "P-FLOWER",
	"pacbid - pacella bid board":              "PACBID",
	"pacella - pacella-facing":                "PACELLA",
	"packair - product handling solutions":    "PACKAIR",
	"pacrail - pacel-rail":                    "PACRAIL",
	"pallets - pallet exchange":               "PALLETS",
	"parman - parman energy group":            "PARMAN",
	"parsip - parsippany, nj":                 "PARSIP",
	"peakrail - peak rail load":               "PEAKRAIL",
	"pearson - pearson construction":          "PEARSON",
	"pen-drop - penske drop trailer":          "PEN-DROP",
	"penn - penn power group":                 "PENN",
	"penskdbl - penske dbls":                  "PENSKDBL",
	"penske - penske expedite":                "PENSKE",
	"pepsi - pepsi logistics":                 "PEPSI",
	"pepsigpi - pepsi/gpi":                    "PEPSIGPI",
	"pepspizz - pep's pizza co":               "PEPSPIZZ",
	"pepteam - pepsi/gpi team service":        "PEPTEAM",
	"petfood - ainsworth pet food":            "PETFOOD",
	"pfg - performance food group":            "PFG",
	"pfs2stop - pfs 2 stop load rates":        "PFS2STOP",
	"pilg-hot - pilgrims - hot shipments":     "PILG-HOT",
	"pilgcopk - pilgrims co-pack":             "PILGCOPK",
	"pilgrims - pilgrims loads":               "PILGRIMS",
	"pilgteam - pilgrims - team shipments":    "PILGTEAM",
	"planpac - planned pacella rail":          "PLANPAC",
	"plp - premier logistics partners":        "PLP",
	"pods - pods":                             "PODS",
	"polar3pl - polar 3pl loads":              "POLAR3PL",
	"poolpal - my pool pal":                   "POOLPAL",
	"portland - spopor":                       "PORTLAND",
	"poultry - gold creek processing llc":     "POULTRY",
	"primal - primal racing group":            "PRIMAL",
	"printpro - printpro":                     "PRINTPRO",
	"protran - protran auto parts":            "PROTRAN",
	"pubclout - publix clean outs":            "PUBCLOUT",
	"publix - publix edi loads":               "PUBLIX",
	"publixpo - publix power only":            "PUBLIXPO",
	"publstor - publix store runs":            "PUBLSTOR",
	"publstr2 - publix store runs 2":          "PUBLSTR2",
	"pubreef - publix reefer":                 "PUBREEF",
	"pubshell - publix shell loads":           "PUBSHELL",
	"pwronly - power only":                    "PWRONLY",
	"px-adhoc - px adhoc loads":               "PX-ADHOC",
	"px-air - px-ups air route":               "PX-AIR",
	"px-dbls - px doubles load":               "PX-DBLS",
	"px-pup - ups px pup trailer loads":       "PX-PUP",
	"px-rail - px rail":                       "PX-RAIL",
	"px-ups - ups px project":                 "PX-UPS",
	"quad - quad":                             "QUAD",
	"qvc - qvc coyote loads":                  "QVC",
	"rahabs - rahabs rope":                    "RAHABS",
	"rai - fabrications":                      "RAI",
	"raven - fec-raven":                       "RAVEN",
	"rcslloca - freight forwarder":            "RCSLLOCA",
	"redmond - redmond, wa":                   "REDMOND",
	"redvsuga - red v foods":                  "REDVSUGA",
	"reefer - reefer freight":                 "REEFER",
	"relay - pacella amazon adhoc":            "RELAY",
	"repower - repower load":                  "REPOWER",
	"rework - rework":                         "REWORK",
	"ricchick - rich chicks":                  "RICCHICK",
	"richelie - richelieu american ltd":       "RICHELIE",
	"roadie - roadiexd":                       "ROADIE",
	"roadrunn - roadrunner":                   "ROADRUNN",
	"rockhill - rockhill foods":               "ROCKHILL",
	"rockymtn - padd4: rocky mtn - fuel reg":  "ROCKYMTN",
	"royston - freight of all kind":           "ROYSTON",
	"rpcfrt - rehrig pacific comapny frt":     "RPCFRT", //nolint:misspell // From TMS
	"ruan - ruan adhoc loads":                 "RUAN",
	"ruanauto - raun automotive":              "RUANAUTO",
	"ruancln - ruan cleanout":                 "RUANCLN",
	"ruanded - ruan dedicated":                "RUANDED",
	"ruanpubl - ruan publix loads":            "RUANPUBL",
	"ruantarg - ruan target loads":            "RUANTARG",
	"ruantemp - ruan reefer":                  "RUANTEMP",
	"rudolph - hot christmas eve shipments":   "RUDOLPH",
	"ruiz - ruiz foods":                       "RUIZ",
	"rxo - rxo expedite":                      "RXO",
	"rxo-bt - rxo boxtruck":                   "RXO-BT",
	"rxo-fb - rxo flatbead loads":             "RXO-FB",
	"rxo-hpro - rxo hazmat project":           "RXO-HPRO",
	"rxo-mx - rxo mexico adhoc":               "RXO-MX",
	"rxo-po - rxo power only":                 "RXO-PO",
	"rxo-proj - rxo project loads":            "RXO-PROJ",
	"rxo-rt - rxo round trip":                 "RXO-RT",
	"rxo-sv - rxo sprinter van":               "RXO-SV",
	"ryder - ryder temp":                      "RYDER",
	"s3pl - superior 3rd party logistics":     "S3PL",
	"sails - sail restaurant":                 "SAILS",
	"saks - saks off 5th":                     "SAKS",
	"samsung - samsung-neovia logistics":      "SAMSUNG",
	"sanco - sanco equipment":                 "SANCO",
	"santa - hot christmas shipments":         "SANTA",
	"schwans - schwans":                       "SCHWANS",
	"scigames - scientific games":             "SCIGAMES",
	"sdi - syfan dedicated load":              "SDI",
	"seattle - sposea":                        "SEATTLE",
	"secaucus - secaucus,nj":                  "SECAUCUS",
	"selit - selit north america, inc":        "SELIT",
	"sensient - sensient technologies co":     "SENSIENT",
	"sfc - schwans company":                   "SFC",
	"sfl - foundation":                        "SFL",
	"sha-chp - shasta - chep":                 "SHA-CHP",
	"shasta - shasta beverage":                "SHASTA",
	"shaw - shaw flooring products":           "SHAW",
	"shealy - dealership":                     "SHEALY",
	"shifters - moving trailers":              "SHIFTERS",
	"signode - signode":                       "SIGNODE",
	"sleepnum - sleep number":                 "SLEEPNUM",
	"smithfld - smithfield loads":             "SMITHFLD",
	"smitla - smitty's supply, inc":           "SMITLA",
	"smuckers - smuckers":                     "SMUCKERS",
	"south - southwire":                       "SOUTH",
	"sparky - sparky trans":                   "SPARKY",
	"spire - spire collective":                "SPIRE",
	"spokane - spokane delivery":              "SPOKANE",
	"spot - spot quote":                       "SPOT",
	"sptcnatl - spot central atlantic":        "SPTCNATL",
	"sptctatl - spot central atlantic":        "SPTCTATL",
	"sptg - south port transpor group":        "SPTG",
	"sptgucst - spot gulf coast":              "SPTGUCST",
	"sptloatl - spot - lower atlantic":        "SPTLOATL",
	"sptmidw - spot - midwest":                "SPTMIDW",
	"srsdist - bulding products":              "SRSDIST",
	"standby - ups standby":                   "STANDBY",
	"starbcks - nestle starbucks":             "STARBCKS",
	"starbks - nestle starbucks loads":        "STARBKS",
	"stawater - stagewater":                   "STAWATER",
	"stlcell - jail cells":                    "STLCELL",
	"stlcelod - jail cells over dim":          "STLCELOD",
	"stodry - nestle sto dry loads":           "STODRY",
	"stotemp - nestle sto temp loads":         "STOTEMP",
	"stotrl - stoughton trailer leasing":      "STOTRL",
	"stpaul - saint paul":                     "STPAUL",
	"stratesu - strategic telecom supply":     "STRATESU",
	"sunbelt - equipment and tools":           "SUNBELT",
	"superior - superior dairy":               "SUPERIOR",
	"superval - supervalue":                   "SUPERVAL",
	"swift - swift trailer  moves":            "SWIFT",
	"syfan - syfan":                           "SYFAN",
	"t-chassi - trac chassis":                 "T-CHASSI",
	"talenti - talenti":                       "TALENTI",
	"tarps - tarps":                           "TARPS",
	"tbc/fdsi - tire battery company":         "TBC/FDSI",
	"team - team load":                        "TEAM",
	"teamplts - team & pallets exchange":      "TEAMPLTS",
	"telos - telos logistics":                 "TELOS",
	"temp - temp controlled loads":            "TEMP",
	"temp rec - temp recorder":                "TEMP REC",
	"tender - tendergrass":                    "TENDER",
	"terberg - taylor big red - terminal tr":  "TERBERG",
	"test - test load":                        "TEST",
	"tforce - tforce":                         "TFORCE",
	"tforce!! - hot loads - loaded each way":  "TFORCE!!",
	"tfordrop - tforce drop":                  "TFORDROP",
	"tforrail - tforce rail":                  "TFORRAIL",
	"thermo - thermofisher/ plastic parts":    "THERMO",
	"tico - spotter trucks":                   "TICO",
	"tier1mex - tier 1 mexico (ryder)":        "TIER1MEX",
	"tierone - ryder tier one":                "TIERONE",
	"tkw - thermo king":                       "TKW",
	"tofcdbls - ups tofc dbls loads":          "TOFCDBLS",
	"totalql - total quality logistics":       "TOTALQL",
	"tower - tower automotive":                "TOWER",
	"toyota - toyota automotove group":        "TOYOTA", //nolint:misspell // From TMS
	"trac - trac intermodal":                  "TRAC",
	"tradepro - tradepro":                     "TRADEPRO",
	"trailer - trailer move or repair":        "TRAILER",
	"transexp - trans expedite":               "TRANSEXP",
	"tri-stat - tri state":                    "TRI-STAT",
	"trlmoves - trailer moves":                "TRLMOVES",
	"tslinbnd - tsl-inbound from customer":    "TSLINBND",
	"tsllease - tsl-lease outgoing units":     "TSLLEASE",
	"tslrent - tsl-rental outgoing units":     "TSLRENT",
	"tslsales - tsl-sales outgoing units":     "TSLSALES",
	"tslswap - tsl-swap units out":            "TSLSWAP",
	"turbo - turbo truck center":              "TURBO",
	"twcwinat - tcw logistics":                "TWCWINAT",
	"tyson - tyson loads":                     "TYSON",
	"tysonhot - tyson - hot shipment":         "TYSONHOT",
	"tysoteam - tyson - team shipment":        "TYSOTEAM",
	"una blin - una blind shipment":           "UNA BLIN",
	"una blpl - una blind/pallets":            "UNA BLPL",
	"una hot - una hot load":                  "UNA HOT",
	"una plts - una pallet exchange load":     "UNA PLTS",
	"una tarp - una tarp load":                "UNA TARP",
	"una team - una team load":                "UNA TEAM",
	"una ups - una ups load":                  "UNA UPS",
	"una usps - una usps load":                "UNA USPS",
	"unaupsar - una ups air load":             "UNAUPSAR",
	"unfiga - groceries":                      "UNFIGA",
	"unigrp - unigroup":                       "UNIGRP",
	"unis - unis transportation":              "UNIS",
	"upfrthaz - ups frt hazmat loads":         "UPFRTHAZ",
	"ups - ups load":                          "UPS",
	"ups air - ups air load":                  "UPS AIR",
	"ups-ftd - ups ftd loads":                 "UPS-FTD",
	"ups-hot - hot ups shipment":              "UPS-HOT",
	"upsalcon - alcon-ups loads":              "UPSALCON",
	"upsamzn - ups amazon":                    "UPSAMZN",
	"upsastra - astra zeneca shipments":       "UPSASTRA",
	"upscargo - ups air cargo loads":          "UPSCARGO",
	"upscarrd - ups carrier direct":           "UPSCARRD",
	"upscepw - ups exp pratt&whitney":         "UPSCEPW",
	"upscstby - ups carrier direct standby":   "UPSCSTBY",
	"upsdbls - ups doubles":                   "UPSDBLS",
	"upsdray - ups dray moves":                "UPSDRAY",
	"upsdship - ups direct ship":              "UPSDSHIP",
	"upsent - ups entertainment":              "UPSENT",
	"upserrec - up service recovery":          "UPSERREC",
	"upsexp - ups express critical":           "UPSEXP",
	"upsffwd - ups freight forwarding":        "UPSFFWD",
	"upsflwr - upsflower":                     "UPSFLWR",
	"upsfnest - ups freight nestle":           "UPSFNEST",
	"upsfr-ch - upsfreight charlotte":         "UPSFR-CH",
	"upsfrt - ups freight":                    "UPSFRT",
	"upsftnst - ups freight nestle":           "UPSFTNST",
	"upshlth - ups healthcare":                "UPSHLTH",
	"upshr - ups - hello fresh":               "UPSHR",
	"upsintl - ups international loads":       "UPSINTL",
	"upslocal - local work":                   "UPSLOCAL",
	"upslupin - ups lupin":                    "UPSLUPIN",
	"upsnaaf - n.amer air frt":                "UPSNAAF",
	"upspdrop - ups proflower drop":           "UPSPDROP",
	"upsptemp - ups proflower live":           "UPSPTEMP",
	"upspup - ups pup":                        "UPSPUP",
	"upsscs - ups supply chain solutions":     "UPSSCS",
	"upssmart - ups smart loads":              "UPSSMART",
	"upssmdbl - ups smart doubles":            "UPSSMDBL",
	"upstofc - ups tofc- cach loads":          "UPSTOFC",
	"upstran - ups transportation loads":      "UPSTRAN",
	"upswock - ups wockhardt":                 "UPSWOCK",
	"uscab - us cabinet depot":                "USCAB",
	"usp-mask - uspostal mask":                "USP-MASK",
	"uspostal - u.s.post office load":         "USPOSTAL",
	"usps - usps load":                        "USPS",
	"uspsfrt - usps freight auction":          "USPSFRT",
	"uspslbx - usps lcl box no ppw":           "USPSLBX",
	"uspsldt - usps ldt":                      "USPSLDT",
	"uspspeak - usps peak 2023":               "USPSPEAK",
	"uspsupdt - usps update":                  "USPSUPDT",
	"vantix - vantix food":                    "VANTIX",
	"vantixd - vantix dry van":                "VANTIXD",
	"vantixr - vantix reefer":                 "VANTIXR",
	"vascor - vascor limited shipments":       "VASCOR",
	"vdl - event products":                    "VDL",
	"vdray - volvo ocean drayage":             "VDRAY",
	"vensun - vensun pharm. loads":            "VENSUN",
	"ventalfs - ventura al ob fuel":           "VENTALFS",
	"ventgafs - ventura fuel ob ga":           "VENTGAFS",
	"ventohfs - ventura fuel ob oh":           "VENTOHFS",
	"ventpafs - ventura ob pa fuel":           "VENTPAFS",
	"venwifsc - ventura wi fsc":               "VENWIFSC",
	"volbill - volvo ocean load for billing":  "VOLBILL",
	"voldray - volvo ocean drayage":           "VOLDRAY",
	"volhaz - volvo hazmat":                   "VOLHAZ",
	"volkswag - volkswag":                     "VOLKSWAG",
	"volvo - volvo":                           "VOLVO",
	"volvo-bt - volvo box truck":              "VOLVO-BT",
	"volvo-fb - volvo flatbed load":           "VOLVO-FB",
	"volvo-mx - volvo mexico":                 "VOLVO-MX",
	"volvo-sv - volvo sprinter van loads":     "VOLVO-SV",
	"volvopro - volvo project":                "VOLVOPRO",
	"waldry - walmart dry":                    "WALDRY",
	"walmedi - wal mart dry van":              "WALMEDI",
	"waltemp - walmart temp":                  "WALTEMP",
	"wayn-hot - wayne farms - hot shipment":   "WAYN-HOT",
	"wayne - wayne farms - ch robinson":       "WAYNE",
	"waynteam - wayne farms - team shipment":  "WAYNTEAM",
	"web hot - website maintainence hot":      "WEB HOT",  //nolint:misspell // From TMS
	"web plts - website maintainence plt x":   "WEB PLTS", //nolint:misspell // From TMS
	"web team - website maintainence team":    "WEB TEAM", //nolint:misspell // From TMS
	"web-cont - website - contract loads":     "WEB-CONT",
	"web-spot - website - spot market loads":  "WEB-SPOT",
	"website - website maintainence":          "WEBSITE", //nolint:misspell // From TMS
	"wells - blue bunny ice cream":            "WELLS",
	"westcst - padd5: west coast fuel reg":    "WESTCST",
	"whcastle - white castle loads":           "WHCASTLE",
	"wilcorp - wilcorp":                       "WILCORP",
	"willam - william r hill":                 "WILLAM",
	"wisllab - wisconsin lighting lab":        "WISLLAB",
	"withers - withers worldwide":             "WITHERS",
	"wmrhill - wm r hill":                     "WMRHILL",
	"woodadt1 - woodgrain dry van":            "WOODADT1",
	"woodarro - woodstock/arrons":             "WOODARRO",
	"woodgran - woodgrain":                    "WOODGRAN",
	"woodgrn - woodgrain":                     "WOODGRN",
	"worma - worcester,ma":                    "WORMA",
	"xpobrk - xpo- brokerage":                 "XPOBRK",
	"xpodod - dod loads":                      "XPODOD",
	"xponlm - xpo/nlm expedite":               "XPONLM",
	"yrc - yrc freight load":                  "YRC",
	"yrdshift - ups yard shifter":             "YRDSHIFT",
	"zion - zion solutions group":             "ZION",
	"zteam - z team ups amazon loads":         "ZTEAM",
}

// Reverse map from code to label for Syfan
var syfanOrderCodeToLabel = map[string]string{
	"3mload":   "3mload - 3m loads",
	"abbott":   "abbott - abbott nutrition",
	"abicafsc": "abicafsc - abi cali fuel",
	"abireef":  "abireef - anheuser busch reefer load",
	"ace":      "ace - ace hardware",
	"actaero":  "actaero - active aero shipments",
	"actcan":   "actcan - actaero canada",
	"actimex":  "actimex - active aero mexico shipments",
	"aerotemp": "aerotemp - active aero temp controlled",
	"agildrop": "agildrop - agile cold export drop",
	"agilecol": "agilecol - agile cold storage exports",
	"agillive": "agillive - agile cold exports live",
	"agilmaco": "agilmaco - agile macon",
	"agilproj": "agilproj - agile special project",
	"agrosup":  "agrosup - agrosuper",
	"ainswrth": "ainswrth - transplace - ainsworth",
	"airexp":   "airexp - ups air team load",
	"airexs":   "airexs - ups air solo load",
	"akpizza":  "akpizza - port city bakery dba ak pizz",
	"albaf":    "albaf - alba form inc",
	"alliriwi": "alliriwi - alliance laundry",
	"allsbuga": "allsbuga - allsouth sprikler",
	"allsourc": "allsourc - allsource supply inc",
	"amazon":   "amazon - amazon (direct shipments)",
	"amazonga": "amazonga - amazon project",
	"ambient":  "ambient - ambient loads",
	"amercolr": "amercolr - american color",
	"amerdega": "amerdega - ameripipe",
	"americol": "americol - americold",
	"amick":    "amick - amick farms",
	"amksolo":  "amksolo - amick farms solo",
	"amkteam":  "amkteam - amick farms team",
	"amrelay":  "amrelay - amazon relay",
	"anhbsch":  "anhbsch - anheuser busch",
	"anhebusc": "anhebusc - anheuser busch",
	"anncmt":   "anncmt - announcment convertors",
	"aod-gm":   "aod-gm - active aero- gm",
	"apex":     "apex - apex logistics",
	"aplhot":   "aplhot - apl hot shipments",
	"apllog":   "apllog - apl logistics",
	"apn":      "apn - apnonweiler",
	"aprile":   "aprile - aprile",
	"aps":      "aps - equipment",
	"arcbest":  "arcbest - arcbest loads",
	"armada":   "armada - armada shipment",
	"armadhot": "armadhot - armada - hot shipment",
	"armateam": "armateam - armada - team shipment",
	"arrow":    "arrow - arrowstream",
	"athens":   "athens - athens stone casting",
	"autocar":  "autocar - yarddog",
	"availabl": "availabl - available load - booked sub",
	"averitt":  "averitt - glass",
	"ax-empty": "ax-empty - axis empty trailer moves",
	"axis":     "axis - axis warehouse moves",
	"axis-nor": "axis-nor - axis norfol",
	"azmachin": "azmachin - az machine",
	"backup":   "backup - back up truck",
	"beasley":  "beasley - beasley freight",
	"berk":     "berk - berk enterprises inc",
	"big4":     "big4 - auto lubrucation",
	"binder":   "binder - binderholz",
	"bitzer":   "bitzer - bitzer us, inc",
	"blind":    "blind - blind shipment",
	"blindhot": "blindhot - blind shipment hot load",
	"blindplt": "blindplt - blind shipmt/pallet exchange",
	"blocks":   "blocks - drink blocks llc",
	"bluebird": "bluebird - blue bird body company",
	"bluebuff": "bluebuff - blue buffalo",
	"bluewat":  "bluewat - blue water",
	"bndray":   "bndray - bnsf dray moves",
	"bnsf":     "bnsf - bnsf rail recovery",
	"bnsf-hot": "bnsf-hot - bnsf hot loads",
	"bnsf-mw":  "bnsf-mw - bnsf cicero",
	"bnsfdbls": "bnsfdbls - bnsf doubles",
	"bnsfhaz":  "bnsfhaz - bnsf hazmat",
	"bnsflog":  "bnsflog - bnsf logistics",
	"bnsfotr":  "bnsfotr - bnsf rail recovery",
	"bnsfpup":  "bnsfpup - pup trailer",
	"bodyarmr": "bodyarmr - bodyarmour",
	"boone":    "boone - boone trailer moves",
	"boonelog": "boonelog - boone logistics",
	"boonepln": "boonepln - boone planned loads",
	"borg":     "borg - borg warner",
	"bourbros": "bourbros - bourbon brothers",
	"brand":    "brand - brand energy",
	"bright":   "bright - bright farms",
	"buitoni":  "buitoni - buitoni foods loads",
	"bulk":     "bulk - bulk head required",
	"burtmd":   "burtmd - burtonsville, md",
	"bycowi":   "bycowi - byco",
	"cabworld": "cabworld - cab worldwide",
	"camco":    "camco - camco",
	"capital":  "capital - capital lighting",
	"careline": "careline - careline inc",
	"carg-hot": "carg-hot - cargill - hot shipment",
	"cargill":  "cargill - cargill shipments",
	"cargteam": "cargteam - cargill - team shipment",
	"carlex":   "carlex - carlex - auto glass",
	"cason":    "cason - cason",
	"catrplr":  "catrplr - caterpillar loads",
	"cbears":   "cbears - charlie bears",
	"cbtrans":  "cbtrans - cb transportation",
	"cdc":      "cdc - cdc",
	"cdctemp":  "cdctemp - cdc temp control",
	"cejoes":   "cejoes - travelmation",
	"celeb":    "celeb - celeb luxury",
	"cenatl":   "cenatl - central atlantic",
	"ceva":     "ceva - ceva logistics",
	"chainlnk": "chainlnk - chainlink",
	"chassis":  "chassis - chassis leg",
	"chelmma":  "chelmma - chelmsford, ma",
	"chemtrea": "chemtrea - chemtreat",
	"chflower": "chflower - ch robinson flowers",
	"chicago":  "chicago - chicago,il",
	"chilled":  "chilled - chilled loads",
	"chipotle": "chipotle - chipotle loads",
	"chrysler": "chrysler - chrysler automotive",
	"coilp+":   "coilp+ - coilplus",
	"coke":     "coke - coca-cola",
	"colonial": "colonial - colonial materials",
	"conagra":  "conagra - conagra",
	"conwadd":  "conwadd - conway - addhock",
	"conway":   "conway - conway/xpo",
	"copper":   "copper - poultry broker",
	"corrim":   "corrim - corrim doors",
	"coyopeak": "coyopeak - coyote trl moves",
	"coyote":   "coyote - coyote loads",
	"crane":    "crane - crane solutions",
	"crescent": "crescent - crescent kayaks",
	"critical": "critical - failure not an option",
	"crysfeed": "crysfeed - crystal feed loads",
	"csm-hot":  "csm-hot - csm - hot shipment",
	"csm-team": "csm-team - csm - team shipment",
	"csmbake":  "csmbake - csm bakery shipment",
	"csx":      "csx - csx service recovery",
	"csx-hot":  "csx-hot - csx - hot loads",
	"csx-shut": "csx-shut - csx shuttle moves",
	"csxchass": "csxchass - csx w/ chassis",
	"csxd2d":   "csxd2d - csx door to door",
	"csxlocal": "csxlocal - csx local",
	"csxpup":   "csxpup - csx pup trailer",
	"ctvtrans": "ctvtrans - ctv transportation",
	"danone":   "danone - danone",
	"dart":     "dart - dart containers",
	"dblblind": "dblblind - double blind",
	"dblsair":  "dblsair - ups doubles air load",
	"deepchil": "deepchil - deep chill 28 degrees",
	"deepfrz":  "deepfrz - deep freeze -20",
	"deepsout": "deepsout - terminal tractors",
	"delmonte": "delmonte - del monte loads",
	"delta":    "delta - delta cargo load",
	"detent":   "detent - detention",
	"dhl":      "dhl - dhl global forwarding",
	"dillard":  "dillard - dillards gulf coast",
	"docklev":  "docklev - dock levelers",
	"dod":      "dod - dept of defense",
	"dole":     "dole - dole",
	"donation": "donation - donation load",
	"double":   "double - doubles qualified",
	"drilchm":  "drilchm - drillchem",
	"drink":    "drink - drink blocks llc",
	"drop":     "drop - georgia drop trailer project",
	"drop-amb": "drop-amb - nestle syfk - ambient",
	"drop-chl": "drop-chl - nestle syfk - chilled",
	"drop-frz": "drop-frz - nestle syfk - frozen",
	"drop-tmp": "drop-tmp - nestle syfk- temp controlled",
	"droptrl":  "droptrl - drop trailer",
	"drpepper": "drpepper - dr.pepper/7up loads",
	"dry":      "dry - dry freight",
	"dsi":      "dsi - distribution services intern",
	"duke":     "duke - energy company",
	"eapolfo":  "eapolfo - eastern poultry foods",
	"eastcst":  "eastcst - padd1:east coast - fuel",
	"echo":     "echo - echo",
	"edi":      "edi - edi - load updated via edi",
	"edi blin": "edi blin - edi load blind shipment",
	"edi hot":  "edi hot - edi updated hot load via edi",
	"edi plts": "edi plts - edi load pallet xchang",
	"elkay":    "elkay - elkay interior systems",
	"elliott":  "elliott - richard a elliott trans",
	"emmaus":   "emmaus - emmaus foods",
	"ennis-pa": "ennis-pa - ennis paint",
	"enru":     "enru - enru",
	"eptusa":   "eptusa - eptusa",
	"eskimo":   "eskimo - eskimo cold storage",
	"exp":      "exp - ups expedited team",
	"exp-pup":  "exp-pup - ups expedited team pup",
	"expadhoc": "expadhoc - exp adhoc loads",
	"expair":   "expair - ups expedited air team",
	"expdbl":   "expdbl - exp doubles",
	"expmeds":  "expmeds - ups express crit. pharm.",
	"expmilt":  "expmilt - ups express crit. military",
	"exprail":  "exprail - ups team rail",
	"exs":      "exs - ups expedited solo",
	"exs-pup":  "exs-pup - ups expedited solo pup",
	"exsadhoc": "exsadhoc - exs adhoc loads",
	"exsair":   "exsair - ups expedited air solo",
	"exsdbl":   "exsdbl - exs doubles",
	"exsrail":  "exsrail - solo rail",
	"farmer":   "farmer - farmer focus",
	"fca":      "fca - fiat chrysler automotive",
	"fcadrop":  "fcadrop - fca drop and hook",
	"fcarail":  "fcarail - fca rail moves",
	"fdxadhoc": "fdxadhoc - fedex frt adhoc",
	"fdxdbl":   "fdxdbl - fedex doubles",
	"fdxshift": "fdxshift - fedex express yard shifters",
	"fecrail":  "fecrail - florida e.coast railway",
	"fedadhoc": "fedadhoc - fedex adhoc loads",
	"fedex":    "fedex - fedex ground loads",
	"fedexatl": "fedexatl - atl fed exp local",
	"fedexcc":  "fedexcc - fedex custom critical",
	"fedexded": "fedexded - fedex freight dedicated",
	"fedexhub": "fedexhub - fedex hub moves",
	"fedexprs": "fedexprs - fedex express",
	"fedextb":  "fedextb - fedex truckload brokerage",
	"fedfrt":   "fedfrt - fedex freight",
	"fedrop":   "fedrop - fedex drop",
	"fedstand": "fedstand - fedex standby team",
	"fedxtemp": "fedxtemp - fedex temp control",
	"fema":     "fema - fema",
	"fenix":    "fenix - fenix parts-gainesville",
	"ferrcand": "ferrcand - ferrara candy",
	"fieldale": "fieldale - fieldale",
	"finlays":  "finlays - finlays loads",
	"fitco":    "fitco - fitco loads",
	"flatbed":  "flatbed - flatbed load",
	"fldturf":  "fldturf - field turf",
	"flexfilm": "flexfilm - flex film",
	"flexngat": "flexngat - flex n gate",
	"flexport": "flexport - flexport dba deliverr",
	"flowerwd": "flowerwd - flowerwood",
	"flyfresh": "flyfresh - flying fresh usa",
	"foodlion": "foodlion - food lion loads",
	"foodtran": "foodtran - foodintransit",
	"ford250":  "ford250 - ford fsc 1-250 miles",
	"ford500":  "ford500 - ford fsc 250-500 miles",
	"ford501":  "ford501 - ford fsc 500+ miles",
	"forddrop": "forddrop - ford drop and hook",
	"formex":   "formex - formex",
	"frozen":   "frozen - frozen loads",
	"frthawk":  "frthawk - freighthawk loads",
	"fscauto":  "fscauto - fcsautoparts",
	"fusionf":  "fusionf - fusion furniture, inc",
	"garimark": "garimark - garimark",
	"gchda100": "gchda100 - honda gulf 0-100 mi",
	"gchda300": "gchda300 - honda gulf 101-300 mi",
	"gchda800": "gchda800 - honda gulf 301-800 mi",
	"gchda801": "gchda801 - honda gulf 800+ mi",
	"ge":       "ge - ge appliances",
	"genmills": "genmills - general mills",
	"geracks":  "geracks - rack return",
	"gerdau":   "gerdau - gerdau loads",
	"geveko":   "geveko - thermoplastic sheets",
	"gldcreek": "gldcreek - gold creek foods",
	"globex":   "globex - globex international inc",
	"globxotr": "globxotr - globex international inc otr",
	"gm parts": "gm parts - gm parts",
	"gm-can":   "gm-can - gm canada",
	"gm-cca":   "gm-cca - gm rxo loads",
	"gm-dn":    "gm-dn - gm drop new",
	"gm-drop":  "gm-drop - gm drop & hook",
	"gm-dun":   "gm-dun - gm empty racks",
	"gm-hot":   "gm-hot - gm hot loads",
	"gm-mex":   "gm-mex - mexico shipments",
	"gm-misc":  "gm-misc - gm misc - not tracked in lms",
	"gm-mxdrp": "gm-mxdrp - gm mexico drop & hook loads",
	"gm-pn":    "gm-pn - gm parts new",
	"gm-racks": "gm-racks - gm rack load",
	"gm-share": "gm-share - gm-share the spare",
	"gm-tank":  "gm-tank - tanker loads",
	"gm-wknd":  "gm-wknd - gm weekend production shipme",
	"gmcca":    "gmcca - xpo/gmcca",
	"gmcca-mx": "gmcca-mx - gmcca/menlo - mexico",
	"gmhazmat": "gmhazmat - car parts- hazardous",
	"gmptemp":  "gmptemp - gm parts temp",
	"godawgs":  "godawgs - uga shipments",
	"gorfoods": "gorfoods - gordon food loads",
	"grap-hot": "grap-hot - graphic pkg - hot shipment",
	"graphic":  "graphic - graphic packaging",
	"grapteam": "grapteam - graphic team load",
	"greennc":  "greennc - greensboro,nc",
	"grove":    "grove - grove services",
	"groveotr": "groveotr - grove services otr",
	"gtr":      "gtr - gateway truck and refrigerat",
	"gulfcst":  "gulfcst - padd3: gulf coast fuel reg",
	"gulfcstf": "gulfcstf - gulf coast",
	"gulfsout": "gulfsout - gulf south forest products",
	"hamilton": "hamilton - hamilton group",
	"happtafl": "happtafl - happy's home centers inc",
	"hazbatt":  "hazbatt - atl motive power shipments",
	"hazmat":   "hazmat - hazardous materials",
	"hecny":    "hecny - hecny transportation",
	"hermor":   "hermor - hermiston, or",
	"hfc-part": "hfc-part - hospitality freight- partial",
	"hhl":      "hhl - hormel jot hhl loads",
	"hmltmgrp": "hmltmgrp - hamilton group llc.",
	"hna-rack": "hna-rack - honda rack loads",
	"hogan":    "hogan - hogan",
	"homedept": "homedept - home depot loads",
	"honda":    "honda - honda expedites",
	"honda-ad": "honda-ad - honda adhoc",
	"honda100": "honda100 - honda 0-100 miles",
	"honda300": "honda300 - honda 101-300 miles",
	"honda800": "honda800 - honda 301-800 miles",
	"honda801": "honda801 - honda over 801 miles",
	"honebear": "honebear - hone bear",
	"hordairy": "hordairy - horizon dairy / uber freight",
	"horizon":  "horizon - horizon air freight",
	"hormel":   "hormel - hormel foods",
	"hormelr":  "hormelr - hormel reefer",
	"hormelv":  "hormelv - hormel dry",
	"horsham":  "horsham - horsham,pa",
	"hot":      "hot - hot load",
	"hot plts": "hot plts - hot load pallet exchange",
	"huber":    "huber - huber",
	"hubfrt":   "hubfrt - hubgroup adhoc freight",
	"hubgroup": "hubgroup - hub group",
	"hubharb":  "hubharb - harbor freight",
	"hvac":     "hvac - 1 clear choice hvac",
	"icecream": "icecream - ice cream -20f",
	"igs":      "igs - integratedglobal",
	"indigo":   "indigo - indigo energy",
	"ingles":   "ingles - ingles",
	"intebeoh": "intebeoh - packaging",
	"interra":  "interra - interra international",
	"intervis": "intervis - intervision foods",
	"ironhrs":  "ironhrs - iron horse austions",
	"jaxfl":    "jaxfl - jacksonville,fl",
	"jbfood":   "jbfood - jb group",
	"jbmulti":  "jbmulti - j&b multi-stop",
	"jbstate":  "jbstate - j&b state-state",
	"jenni-o":  "jenni-o - jenni-o / hormel",
	"jkylbrwg": "jkylbrwg - jekyll brewing",
	"johndeer": "johndeer - john deer",
	"johnlarm": "johnlarm - john l armitage & co",
	"joyce":    "joyce - joyce-farms",
	"kenworth": "kenworth - auto car kenworth",
	"khazhot":  "khazhot - kubota hazmat hot",
	"kinexo":   "kinexo - kinexo",
	"kinexor":  "kinexor - kinexo reefer",
	"kinexov":  "kinexov - kinexo van",
	"kingint":  "kingint - king's international",
	"kirch":    "kirch - kirchoff",
	"kirsch":   "kirsch - kirsch transportation",
	"koch":     "koch - koch foods",
	"kochdray": "kochdray - koch foods export dray",
	"korman":   "korman - korman",
	"kraft":    "kraft - kraft foods",
	"kraftdry": "kraftdry - kraft dry loads",
	"kt-strap": "kt-strap - kubota straps required",
	"kubdire":  "kubdire - kubota direct hot del",
	"kubhaz":   "kubhaz - kubota hazmat",
	"kubhazho": "kubhazho - kubota hazmat hot",
	"kubhot":   "kubhot - kubota hot",
	"kubota":   "kubota - kubota east",
	"kubwest":  "kubwest - kubota west cali imports",
	"kubwhot":  "kubwhot - kubota west hot",
	"kuehne":   "kuehne - kuehne + nagel",
	"lahda100": "lahda100 - honda lowatl 0-100 mi",
	"lahda300": "lahda300 - honda lowatl 101-300 mi",
	"lahda800": "lahda800 - honda lowatl 301-800 mi",
	"lahda801": "lahda801 - honda lowatl 800+ mi",
	"lakanto":  "lakanto - lakanto",
	"lake":     "lake - lake foods",
	"lamex":    "lamex - lamex foods",
	"land":     "land - land o' lakes",
	"langston": "langston - langston bags",
	"layover":  "layover - layover",
	"lcd":      "lcd - local lcd",
	"lcdshift": "lcdshift - local shifter work",
	"lcl":      "lcl - local lcl",
	"lcldbl":   "lcldbl - local doubles required",
	"lclpup":   "lclpup - local pup",
	"lifeway":  "lifeway - paper products",
	"lineage":  "lineage - lineage freight management l",
	"linecarg": "linecarg - line cargo imports (kubota)",
	"loadlcks": "loadlcks - load locks req",
	"locallcl": "locallcl - local cl",
	"loupford": "loupford - loup ford",
	"loupgm":   "loupgm - loup gm loads",
	"louphaz":  "louphaz - loup haz shipments",
	"louphot":  "louphot - loup hot shipments",
	"louppup":  "louppup - loup pup trailer",
	"louprail": "louprail - loup rail (up rail)",
	"loupsvcr": "loupsvcr - loup service recovery",
	"louptsla": "louptsla - loup tesla",
	"loupwhol": "loupwhol - loup wholesale loads",
	"lowatl":   "lowatl - lower atlantic",
	"lscs":     "lscs - landstar supply chain",
	"ltl":      "ltl - ltl loads",
	"machine":  "machine - machinery",
	"maersk":   "maersk - maersk, inc",
	"manwah":   "manwah - manwah furniture",
	"marine":   "marine - marine-specialties",
	"marubeni": "marubeni - marubeni trans",
	"mauserpk": "mauserpk - mauser packaging solutions",
	"mccain":   "mccain - mccain",
	"mcgriff":  "mcgriff - mcgriff treading",
	"mcmaster": "mcmaster - mcmaster-carr",
	"mec-air":  "mec-air - morrison air load",
	"mec-appl": "mec-appl - morrison express apple",
	"mec-bas":  "mec-bas - morrison - briggs & stratton",
	"mec-mas":  "mec-mas - morrison - masonite",
	"mem-haz":  "mem-haz - memphis bnsf project hazmat",
	"mem-proj": "mem-proj - bnsf memphis project",
	"memphis":  "memphis - memphis project",
	"men-team": "men-team - menlo team",
	"menasha":  "menasha - menasha",
	"menlo":    "menlo - menlo worldwide",
	"meritor":  "meritor - meritor parts shipments",
	"midwest":  "midwest - bnsf il mn",
	"midwestf": "midwestf - padd2: midwest fuel region",
	"milecorp": "milecorp - mile corp trailer project",
	"mincey":   "mincey - mincey marble loads",
	"minn":     "minn - minneapolis, mn",
	"misc":     "misc - miscellaneous",
	"missoula": "missoula - missoula, mt",
	"mnx":      "mnx - mnx ups",
	"mondi":    "mondi - mondi group",
	"montal":   "montal - montgomery,al",
	"montile":  "montile - monterrey tile",
	"morrison": "morrison - morrison express",
	"morsolo":  "morsolo - morrison express corp solo",
	"morteam":  "morteam - morrison express corp team",
	"mps":      "mps - paper product",
	"mri":      "mri - manufacturing resources intl",
	"murray":   "murray - murray plastics",
	"n.bergen": "n.bergen - north bergen,nj",
	"nacarato": "nacarato - truck center",
	"nahda100": "nahda100 - honda nat'avg 0-100 mi",
	"nahda300": "nahda300 - honda nat'avg 101-300 mi",
	"nahda800": "nahda800 - honda nat'avg 301-800 mi",
	"nahda801": "nahda801 - honda nat'avg 800+ mi",
	"nak":      "nak - nal kiln services llc",
	"new cust": "new cust - menasha",
	"newcold":  "newcold - new cold shuttle program",
	"newcus":   "newcus - new customer",
	"newefood": "newefood - new england foods",
	"nextlog":  "nextlog - nextlogistix",
	"nfi":      "nfi - nfi industries",
	"nhstemp":  "nhstemp - nestle nhs temp controlled",
	"nri":      "nri - nri",
	"nshazmat": "nshazmat - ns rail hazmat",
	"nsheavy":  "nsheavy - ns loads 43000+ lbs",
	"nshot":    "nshot - ns rail hot load",
	"nspup":    "nspup - ns rail pup load",
	"nsrail":   "nsrail - norfolk southern recovery",
	"nucor":    "nucor - nucor steel kingman",
	"oasis":    "oasis - oasis consulting services",
	"oldcast":  "oldcast - old castle",
	"orgameti": "orgameti - orgametics llc",
	"ovainn":   "ovainn - ovain novations",
	"overdim":  "overdim - overdimensional",
	"p-flower": "p-flower - publix flowers",
	"pacbid":   "pacbid - pacella bid board",
	"pacella":  "pacella - pacella-facing",
	"packair":  "packair - product handling solutions",
	"pacrail":  "pacrail - pacel-rail",
	"pallets":  "pallets - pallet exchange",
	"parman":   "parman - parman energy group",
	"parsip":   "parsip - parsippany, nj",
	"peakrail": "peakrail - peak rail load",
	"pearson":  "pearson - pearson construction",
	"pen-drop": "pen-drop - penske drop trailer",
	"penn":     "penn - penn power group",
	"penskdbl": "penskdbl - penske dbls",
	"penske":   "penske - penske expedite",
	"pepsi":    "pepsi - pepsi logistics",
	"pepsigpi": "pepsigpi - pepsi/gpi",
	"pepspizz": "pepspizz - pep's pizza co",
	"pepteam":  "pepteam - pepsi/gpi team service",
	"petfood":  "petfood - ainsworth pet food",
	"pfg":      "pfg - performance food group",
	"pfs2stop": "pfs2stop - pfs 2 stop load rates",
	"pilg-hot": "pilg-hot - pilgrims - hot shipments",
	"pilgcopk": "pilgcopk - pilgrims co-pack",
	"pilgrims": "pilgrims - pilgrims loads",
	"pilgteam": "pilgteam - pilgrims - team shipments",
	"planpac":  "planpac - planned pacella rail",
	"plp":      "plp - premier logistics partners",
	"pods":     "pods - pods",
	"polar3pl": "polar3pl - polar 3pl loads",
	"poolpal":  "poolpal - my pool pal",
	"portland": "portland - spopor",
	"poultry":  "poultry - gold creek processing llc",
	"primal":   "primal - primal racing group",
	"printpro": "printpro - printpro",
	"protran":  "protran - protran auto parts",
	"pubclout": "pubclout - publix clean outs",
	"publix":   "publix - publix edi loads",
	"publixpo": "publixpo - publix power only",
	"publstor": "publstor - publix store runs",
	"publstr2": "publstr2 - publix store runs 2",
	"pubreef":  "pubreef - publix reefer",
	"pubshell": "pubshell - publix shell loads",
	"pwronly":  "pwronly - power only",
	"px-adhoc": "px-adhoc - px adhoc loads",
	"px-air":   "px-air - px-ups air route",
	"px-dbls":  "px-dbls - px doubles load",
	"px-pup":   "px-pup - ups px pup trailer loads",
	"px-rail":  "px-rail - px rail",
	"px-ups":   "px-ups - ups px project",
	"quad":     "quad - quad",
	"qvc":      "qvc - qvc coyote loads",
	"rahabs":   "rahabs - rahabs rope",
	"rai":      "rai - fabrications",
	"raven":    "raven - fec-raven",
	"rcslloca": "rcslloca - freight forwarder",
	"redmond":  "redmond - redmond, wa",
	"redvsuga": "redvsuga - red v foods",
	"reefer":   "reefer - reefer freight",
	"relay":    "relay - pacella amazon adhoc",
	"repower":  "repower - repower load",
	"rework":   "rework - rework",
	"ricchick": "ricchick - rich chicks",
	"richelie": "richelie - richelieu american ltd",
	"roadie":   "roadie - roadiexd",
	"roadrunn": "roadrunn - roadrunner",
	"rockhill": "rockhill - rockhill foods",
	"rockymtn": "rockymtn - padd4: rocky mtn - fuel reg",
	"royston":  "royston - freight of all kind",
	"rpcfrt":   "rpcfrt - rehrig pacific comapny frt", //nolint:misspell // From TMS
	"ruan":     "ruan - ruan adhoc loads",
	"ruanauto": "ruanauto - raun automotive",
	"ruancln":  "ruancln - ruan cleanout",
	"ruanded":  "ruanded - ruan dedicated",
	"ruanpubl": "ruanpubl - ruan publix loads",
	"ruantarg": "ruantarg - ruan target loads",
	"ruantemp": "ruantemp - ruan reefer",
	"rudolph":  "rudolph - hot christmas eve shipments",
	"ruiz":     "ruiz - ruiz foods",
	"rxo":      "rxo - rxo expedite",
	"rxo-bt":   "rxo-bt - rxo boxtruck",
	"rxo-fb":   "rxo-fb - rxo flatbead loads",
	"rxo-hpro": "rxo-hpro - rxo hazmat project",
	"rxo-mx":   "rxo-mx - rxo mexico adhoc",
	"rxo-po":   "rxo-po - rxo power only",
	"rxo-proj": "rxo-proj - rxo project loads",
	"rxo-rt":   "rxo-rt - rxo round trip",
	"rxo-sv":   "rxo-sv - rxo sprinter van",
	"ryder":    "ryder - ryder temp",
	"s3pl":     "s3pl - superior 3rd party logistics",
	"sails":    "sails - sail restaurant",
	"saks":     "saks - saks off 5th",
	"samsung":  "samsung - samsung-neovia logistics",
	"sanco":    "sanco - sanco equipment",
	"santa":    "santa - hot christmas shipments",
	"schwans":  "schwans - schwans",
	"scigames": "scigames - scientific games",
	"sdi":      "sdi - syfan dedicated load",
	"seattle":  "seattle - sposea",
	"secaucus": "secaucus - secaucus,nj",
	"selit":    "selit - selit north america, inc",
	"sensient": "sensient - sensient technologies co",
	"sfc":      "sfc - schwans company",
	"sfl":      "sfl - foundation",
	"sha-chp":  "sha-chp - shasta - chep",
	"shasta":   "shasta - shasta beverage",
	"shaw":     "shaw - shaw flooring products",
	"shealy":   "shealy - dealership",
	"shifters": "shifters - moving trailers",
	"signode":  "signode - signode",
	"sleepnum": "sleepnum - sleep number",
	"smithfld": "smithfld - smithfield loads",
	"smitla":   "smitla - smitty's supply, inc",
	"smuckers": "smuckers - smuckers",
	"south":    "south - southwire",
	"sparky":   "sparky - sparky trans",
	"spire":    "spire - spire collective",
	"spokane":  "spokane - spokane delivery",
	"spot":     "spot - spot quote",
	"sptcnatl": "sptcnatl - spot central atlantic",
	"sptctatl": "sptctatl - spot central atlantic",
	"sptg":     "sptg - south port transpor group",
	"sptgucst": "sptgucst - spot gulf coast",
	"sptloatl": "sptloatl - spot - lower atlantic",
	"sptmidw":  "sptmidw - spot - midwest",
	"srsdist":  "srsdist - bulding products",
	"standby":  "standby - ups standby",
	"starbcks": "starbcks - nestle starbucks",
	"starbks":  "starbks - nestle starbucks loads",
	"stawater": "stawater - stagewater",
	"stlcell":  "stlcell - jail cells",
	"stlcelod": "stlcelod - jail cells over dim",
	"stodry":   "stodry - nestle sto dry loads",
	"stotemp":  "stotemp - nestle sto temp loads",
	"stotrl":   "stotrl - stoughton trailer leasing",
	"stpaul":   "stpaul - saint paul",
	"stratesu": "stratesu - strategic telecom supply",
	"sunbelt":  "sunbelt - equipment and tools",
	"superior": "superior - superior dairy",
	"superval": "superval - supervalue",
	"swift":    "swift - swift trailer moves",
	"syfan":    "syfan - syfan",
	"t-chassi": "t-chassi - trac chassis",
	"talenti":  "talenti - talenti",
	"tarps":    "tarps - tarps",
	"tbc/fdsi": "tbc/fdsi - tire battery company",
	"team":     "team - team load",
	"teamplts": "teamplts - team & pallets exchange",
	"telos":    "telos - telos logistics",
	"temp":     "temp - temp controlled loads",
	"temp rec": "temp rec - temp recorder",
	"tender":   "tender - tendergrass",
	"terberg":  "terberg - taylor big red - terminal tr",
	"test":     "test - test load",
	"tforce":   "tforce - tforce",
	"tforce!!": "tforce!! - hot loads - loaded each way",
	"tfordrop": "tfordrop - tforce drop",
	"tforrail": "tforrail - tforce rail",
	"thermo":   "thermo - thermofisher/ plastic parts",
	"tico":     "tico - spotter trucks",
	"tier1mex": "tier1mex - tier 1 mexico (ryder)",
	"tierone":  "tierone - ryder tier one",
	"tkw":      "tkw - thermo king",
	"tofcdbls": "tofcdbls - ups tofc dbls loads",
	"totalql":  "totalql - total quality logistics",
	"tower":    "tower - tower automotive",
	"toyota":   "toyota - toyota automotove group", //nolint:misspell // From TMS
	"trac":     "trac - trac intermodal",
	"tradepro": "tradepro - tradepro",
	"trailer":  "trailer - trailer move or repair",
	"transexp": "transexp - trans expedite",
	"tri-stat": "tri-stat - tri state",
	"trlmoves": "trlmoves - trailer moves",
	"tslinbnd": "tslinbnd - tsl-inbound from customer",
	"tsllease": "tsllease - tsl-lease outgoing units",
	"tslrent":  "tslrent - tsl-rental outgoing units",
	"tslsales": "tslsales - tsl-sales outgoing units",
	"tslswap":  "tslswap - tsl-swap units out",
	"turbo":    "turbo - turbo truck center",
	"twcwinat": "twcwinat - tcw logistics",
	"tyson":    "tyson - tyson loads",
	"tysonhot": "tysonhot - tyson - hot shipment",
	"tysoteam": "tysoteam - tyson - team shipment",
	"una blin": "una blin - una blind shipment",
	"una blpl": "una blpl - una blind/pallets",
	"una hot":  "una hot - una hot load",
	"una plts": "una plts - una pallet exchange load",
	"una tarp": "una tarp - una tarp load",
	"una team": "una team - una team load",
	"una ups":  "una ups - una ups load",
	"una usps": "una usps - una usps load",
	"unaupsar": "unaupsar - una ups air load",
	"unfiga":   "unfiga - groceries",
	"unigrp":   "unigrp - unigroup",
	"unis":     "unis - unis transportation",
	"upfrthaz": "upfrthaz - ups frt hazmat loads",
	"ups":      "ups - ups load",
	"ups air":  "ups air - ups air load",
	"ups-ftd":  "ups-ftd - ups ftd loads",
	"ups-hot":  "ups-hot - hot ups shipment",
	"upsalcon": "upsalcon - alcon-ups loads",
	"upsamzn":  "upsamzn - ups amazon",
	"upsastra": "upsastra - astra zeneca shipments",
	"upscargo": "upscargo - ups air cargo loads",
	"upscarrd": "upscarrd - ups carrier direct",
	"upscepw":  "upscepw - ups exp pratt&whitney",
	"upscstby": "upscstby - ups carrier direct standby",
	"upsdbls":  "upsdbls - ups doubles",
	"upsdray":  "upsdray - ups dray moves",
	"upsdship": "upsdship - ups direct ship",
	"upsent":   "upsent - ups entertainment",
	"upserrec": "upserrec - up service recovery",
	"upsexp":   "upsexp - ups express critical",
	"upsffwd":  "upsffwd - ups freight forwarding",
	"upsflwr":  "upsflwr - upsflower",
	"upsfnest": "upsfnest - ups freight nestle",
	"upsfr-ch": "upsfr-ch - upsfreight charlotte",
	"upsfrt":   "upsfrt - ups freight",
	"upsftnst": "upsftnst - ups freight nestle",
	"upshlth":  "upshlth - ups healthcare",
	"upshr":    "upshr - ups - hello fresh",
	"upsintl":  "upsintl - ups international loads",
	"upslocal": "upslocal - local work",
	"upslupin": "upslupin - ups lupin",
	"upsnaaf":  "upsnaaf - n.amer air frt",
	"upspdrop": "upspdrop - ups proflower drop",
	"upsptemp": "upsptemp - ups proflower live",
	"upspup":   "upspup - ups pup",
	"upsscs":   "upsscs - ups supply chain solutions",
	"upssmart": "upssmart - ups smart loads",
	"upssmdbl": "upssmdbl - ups smart doubles",
	"upstofc":  "upstofc - ups tofc- cach loads",
	"upstran":  "upstran - ups transportation loads",
	"upswock":  "upswock - ups wockhardt",
	"uscab":    "uscab - us cabinet depot",
	"usp-mask": "usp-mask - uspostal mask",
	"uspostal": "uspostal - u.s.post office load",
	"usps":     "usps - usps load",
	"uspsfrt":  "uspsfrt - usps freight auction",
	"uspslbx":  "uspslbx - usps lcl box no ppw",
	"uspsldt":  "uspsldt - usps ldt",
	"uspspeak": "uspspeak - usps peak 2023",
	"uspsupdt": "uspsupdt - usps update",
	"vantix":   "vantix - vantix food",
	"vantixd":  "vantixd - vantix dry van",
	"vantixr":  "vantixr - vantix reefer",
	"vascor":   "vascor - vascor limited shipments",
	"vdl":      "vdl - event products",
	"vdray":    "vdray - volvo ocean drayage",
	"vensun":   "vensun - vensun pharm. loads",
	"ventalfs": "ventalfs - ventura al ob fuel",
	"ventgafs": "ventgafs - ventura fuel ob ga",
	"ventohfs": "ventohfs - ventura fuel ob oh",
	"ventpafs": "ventpafs - ventura ob pa fuel",
	"venwifsc": "venwifsc - ventura wi fsc",
	"volbill":  "volbill - volvo ocean load for billing",
	"voldray":  "voldray - volvo ocean drayage",
	"volhaz":   "volhaz - volvo hazmat",
	"volkswag": "volkswag - volkswag",
	"volvo":    "volvo - volvo",
	"volvo-bt": "volvo-bt - volvo box truck",
	"volvo-fb": "volvo-fb - volvo flatbed load",
	"volvo-mx": "volvo-mx - volvo mexico",
	"volvo-sv": "volvo-sv - volvo sprinter van loads",
	"volvopro": "volvopro - volvo project",
	"waldry":   "waldry - walmart dry",
	"walmedi":  "walmedi - wal mart dry van",
	"waltemp":  "waltemp - walmart temp",
	"wayn-hot": "wayn-hot - wayne farms - hot shipment",
	"wayne":    "wayne - wayne farms - ch robinson",
	"waynteam": "waynteam - wayne farms - team shipment",
	"web hot":  "web hot - website maintainence hot",    //nolint:misspell // From TMS
	"web plts": "web plts - website maintainence plt x", //nolint:misspell // From TMS
	"web team": "web team - website maintainence team",  //nolint:misspell // From TMS
	"web-cont": "web-cont - website - contract loads",
	"web-spot": "web-spot - website - spot market loads",
	"website":  "website - website maintainence", //nolint:misspell // From TMS
	"wells":    "wells - blue bunny ice cream",
	"westcst":  "westcst - padd5: west coast fuel reg",
	"whcastle": "whcastle - white castle loads",
	"wilcorp":  "wilcorp - wilcorp",
	"willam":   "willam - william r hill",
	"wisllab":  "wisllab - wisconsin lighting lab",
	"withers":  "withers - withers worldwide",
	"wmrhill":  "wmrhill - wm r hill",
	"woodadt1": "woodadt1 - woodgrain dry van",
	"woodarro": "woodarro - woodstock/arrons",
	"woodgran": "woodgran - woodgrain",
	"woodgrn":  "woodgrn - woodgrain",
	"worma":    "worma - worcester,ma",
	"xpobrk":   "xpobrk - xpo- brokerage",
	"xpodod":   "xpodod - dod loads",
	"xponlm":   "xponlm - xpo/nlm expedite",
	"yrc":      "yrc - yrc freight load",
	"yrdshift": "yrdshift - ups yard shifter",
	"zion":     "zion - zion solutions group",
	"zteam":    "zteam - z team ups amazon loads",
}
