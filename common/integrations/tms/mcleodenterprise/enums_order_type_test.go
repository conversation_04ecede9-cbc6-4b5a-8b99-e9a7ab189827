package mcleodenterprise

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

func TestToSyfanOrderType(t *testing.T) {
	tests := []struct {
		name  string
		input string
		want  string
	}{
		{
			name:  "code + label",
			input: "ainswrth - transplace - ainsworth",
			want:  "AINSWRTH",
		},
		{
			name:  "code only",
			input: "ainswrth",
			want:  "AINSWRTH",
		},
		{
			name:  "label only",
			input: "transplace - ainsworth",
			want:  "AINSWRTH",
		},
	}

	client, err := New(models.Integration{
		Tenant: TenantSyfan,
	})
	require.NoError(t, err)

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := client.toSyfanOrderType("", test.input)

			require.NoError(t, err)
			require.Equal(t, test.want, got)
		})
	}
}
