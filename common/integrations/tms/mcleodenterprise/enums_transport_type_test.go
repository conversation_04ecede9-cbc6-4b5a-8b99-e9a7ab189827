package mcleodenterprise

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestToSyfanTrailerCode(t *testing.T) {
	tests := []struct {
		name  string
		input string
		want  string
	}{
		{
			name:  "van code + label",
			input: "v - van (dat)",
			want:  "V",
		},
		{
			name:  "van code only",
			input: "V",
			want:  "V",
		},
		{
			name:  "box truck code + label",
			input: "vs22 - 22 foot box truck-9 years old or newer",
			want:  "VS22",
		},
		{
			name:  "box truck code only",
			input: "vs22",
			want:  "VS22",
		},
		{
			name:  "box truck label only",
			input: "24 foot boxtruck - lift gate",
			want:  "24LG",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := toSyfanTrailerCode(test.input)

			require.NoError(t, err)
			require.Equal(t, test.want, got)
		})
	}
}
