package manhattan

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	CyclopsSubmitAppointmentRequest struct {
		models.CyclopsBaseRequest
		Appointments []CyclopsAppointmentData `json:"appointments"`
	}
)

func (m *Manhattan) SubmitAppointment(
	ctx context.Context,
	poNumbers []string,
	_ models.Warehouse,
	_ bool,
	_ string,
	opts ...models.SchedulingOption,
) error {

	options := &models.SchedulingOptions{
		RequestType: models.RequestTypePickup,
	}
	options.Apply(opts...)

	if options.RequestedDate.IsZero() {
		return errors.New("requested date is required")
	}

	if len(poNumbers) == 0 {
		return errors.New("PRO number is required")
	}

	if len(poNumbers) > 1 {
		return fmt.Errorf("only one PRO number allowed, got %d", len(poNumbers))
	}

	if poNumbers[0] == "" {
		return errors.New("PRO number cannot be empty")
	}

	cyclopsReq := CyclopsMakeAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    ManhattanPlatform,
			Action:      models.ActionMakeAppointment,
			UserID:      m.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: m.creds.Username,
				Password: m.creds.Password,
			},
		},
		Appointments: []CyclopsAppointmentData{{
			PoID:            poNumbers[0],
			AppointmentTime: options.RequestedDate.Format("2006-01-02T15:04:05Z07:00"),
		}},
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %w", err)
	}

	var apptResp models.CyclopsMakeAppointmentResponse
	if err = json.Unmarshal(body, &apptResp); err != nil {
		return fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !apptResp.Success {
		return &models.CyclopsError{
			Message: apptResp.Message,
			Errors:  apptResp.Errors,
		}
	}

	return nil
}
