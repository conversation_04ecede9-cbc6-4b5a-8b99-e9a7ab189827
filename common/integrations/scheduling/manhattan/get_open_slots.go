package manhattan

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/common/redis"
)

type (
	CyclopsGetSlotsRequest struct {
		models.CyclopsGetSlotsRequest
		PoID            string `json:"poId"`
		AppointmentType string `json:"appointmentType"`
		AppointmentID   string `json:"appointmentId"`
		FacilityID      string `json:"facilityId"`
		FacilityText    string `json:"facilityText"`
	}
)

func (m *Manhattan) GetOpenSlots(
	ctx context.Context,
	loadTypeID string,
	req models.GetOpenSlotsRequest,
) ([]models.Slot, error) {

	if req.RequestType == "" {
		req.RequestType = models.RequestTypePickup
	}

	if !req.RequestType.IsValid() {
		return nil, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	cyclopsReq := CyclopsGetSlotsRequest{
		CyclopsGetSlotsRequest: models.CyclopsGetSlotsRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    ManhattanPlatform,
				Action:      models.ActionGetOpenSlots,
				UserID:      m.scheduler.Username,
				Credentials: models.CyclopsCredentials{
					Username: m.creds.Username,
					Password: m.creds.Password,
				},
				Tenant: req.Tenant,
			},
			StartDate: req.Start.Format("2006-01-02"),
			EndDate:   req.End.Format("2006-01-02"),
		},
		PoID:            loadTypeID,
		AppointmentType: req.AppointmentType,
		AppointmentID:   req.AppointmentID,
		FacilityID:      req.FacilityID,
		FacilityText:    req.FacilityText,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var slotsResp models.CyclopsGetSlotsResponse
	if err = json.Unmarshal(body, &slotsResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !slotsResp.Success {
		return nil, &models.CyclopsError{
			Message: slotsResp.Message,
			Errors:  slotsResp.Errors,
		}
	}

	return convertToSlots(ctx, slotsResp.Appointments), nil
}

func convertToSlots(
	ctx context.Context,
	appointments []models.CyclopsAppointmentData,
) []models.Slot {

	ctx = log.With(ctx, zap.String("source", string(models.ManhattanSource)))

	slots := make([]models.Slot, 0)
	for _, appt := range appointments {
		if appt.Warehouse == nil || appt.Warehouse.OpenSlots == nil {
			continue
		}

		var times []time.Time
		for _, slot := range appt.Warehouse.OpenSlots {
			t, err := time.Parse("2006-01-02T15:04:05Z", slot.ScheduledTime)
			if err != nil {
				log.Info(
					ctx,
					"Invalid time format for warehouse",
					zap.String("warehouse_name", appt.Warehouse.Name),
					zap.String("scheduled_time", slot.ScheduledTime),
					zap.Error(err),
				)

				continue
			}
			times = append(times, t)
		}

		if len(times) > 0 {
			slot := models.Slot{
				Dock: models.Dock{
					ID: appt.Warehouse.Name,
				},
				Warehouse: models.Warehouse{
					WarehouseID:   appt.Warehouse.ID,
					WarehouseName: appt.Warehouse.Name,
				},
				StartTimes: times,
			}
			slots = append(slots, slot)
		}

		wh := models.Warehouse{
			Source:        models.ManhattanSource,
			WarehouseID:   appt.Warehouse.ID,
			WarehouseName: appt.Warehouse.Name,
		}

		// TODO: associate warehouse models with services
		err := warehouseDB.Upsert(ctx, &wh)
		if err != nil {
			log.Warn(
				ctx,
				"error saving warehouse",
				zap.String("name", appt.Warehouse.Name),
				zap.Error(err),
			)
		}

		key := fmt.Sprintf("%s-%s", models.ManhattanSource, appt.Warehouse.ID)
		if err := redis.SetKey(ctx, key, appt.Warehouse.Name, 24*time.Hour); err != nil {
			log.WarnNoSentry(
				ctx,
				"error setting warehouse in redis",
				zap.String("name", appt.Warehouse.Name),
				zap.Error(err),
			)
		}
	}

	return slots
}
