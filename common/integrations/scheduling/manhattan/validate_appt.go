package manhattan

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	CyclopsValidateAppointmentRequest struct {
		models.CyclopsBaseRequest
		Warehouse Warehouse `json:"warehouse"`
	}

	ValidatedAppointment struct {
		AppointmentID string            `json:"appointmentId"`
		Reference     string            `json:"reference"`
		ScheduledTime string            `json:"scheduledTime"`
		Duration      int               `json:"duration"`
		Location      string            `json:"location"`
		Status        string            `json:"status"`
		Notes         string            `json:"notes"`
		Warehouse     Warehouse         `json:"warehouse"`
		Extended      ValidatedExtended `json:"extended"`
	}

	Warehouse struct {
		Name     string `json:"name"`
		City     string `json:"city"`
		State    string `json:"state"`
		ZipCode  string `json:"zipCode"`
		Country  string `json:"country"`
		Website  string `json:"website"`
		StopType string `json:"stopType"`
	}

	ValidatedExtended struct {
		Facilities map[string]string `json:"facilities"`
	}

	ValidateAppointmentResponse struct {
		Success      bool                   `json:"success"`
		Message      string                 `json:"message"`
		Errors       []string               `json:"errors"`
		Appointments []ValidatedAppointment `json:"appointments"`
	}
)

func (m *Manhattan) ValidateAppointment(
	ctx context.Context,
	poNumbers []string,
	_ models.Warehouse,
	opts ...models.SchedulingOption,
) ([]models.ValidatedPONumber, error) {

	options := &models.SchedulingOptions{
		RequestType: models.RequestTypePickup,
	}
	options.Apply(opts...)

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	// Since PO numbers are not used in the Cyclops request, we only need to make one API call
	// and then create validation results for all PO numbers based on the single response
	cyclopsReq := CyclopsValidateAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    ManhattanPlatform,
			Action:      models.ActionValidateAppointment,
			UserID:      m.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: m.creds.Username,
				Password: m.creds.Password,
			},
			Tenant: options.Tenant,
		},
		Warehouse: Warehouse{
			City:  options.City,
			State: options.State,
		},
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}
	resp.Body.Close()

	var validationResp ValidateAppointmentResponse
	if err = json.Unmarshal(body, &validationResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// Create validation results for all PO numbers based on the single response
	validatedPOs := make([]models.ValidatedPONumber, 0, len(poNumbers))

	if !validationResp.Success {
		// If the API call failed, mark all PO numbers as invalid
		for _, poNumber := range poNumbers {
			validatedPOs = append(validatedPOs, models.ValidatedPONumber{
				PONumber: poNumber,
				IsValid:  false,
				Error:    fmt.Sprintf("Validation failed: %s", validationResp.Message),
			})
		}
		return validatedPOs, nil
	}

	// Determine if validation was successful based on appointments availability
	isValid := len(validationResp.Appointments) > 0
	var facilities map[string]string
	if isValid && len(validationResp.Appointments) > 0 {
		facilities = validationResp.Appointments[0].Extended.Facilities
	}

	// Create validation results for all PO numbers
	for _, poNumber := range poNumbers {
		validatedPO := models.ValidatedPONumber{
			PONumber:   poNumber,
			IsValid:    isValid,
			Facilities: facilities,
		}

		if !isValid {
			validatedPO.Error = "No appointments found for validation"
		}

		validatedPOs = append(validatedPOs, validatedPO)
	}

	return validatedPOs, nil
}
