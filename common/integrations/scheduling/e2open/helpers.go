package e2open

import (
	"fmt"
	"time"
)

// formatAppointmentDate converts a datetime string to MM/DD/YYYY format
func formatAppointmentDate(dateTimeStr string) (string, error) {
	// Try parsing common datetime formats
	formats := []string{
		time.RFC3339,               // 2006-01-02T15:04:05Z07:00
		time.RFC3339Nano,           // 2006-01-02T15:04:05.999999999Z07:00
		"2006-01-02T15:04:05.000Z", // 2025-01-15T10:00:00.000Z
		"2006-01-02T15:04:05Z",     // 2025-01-15T10:00:00Z
		"2006-01-02T15:04:05",      // 2025-01-15T10:00:00
		"2006-01-02",               // 2025-01-15
	}

	for _, format := range formats {
		if t, err := time.Parse(format, dateTimeStr); err == nil {
			return t.Format("01/02/2006"), nil
		}
	}

	return "", fmt.Errorf("unable to parse date: %s", dateTimeStr)
}
