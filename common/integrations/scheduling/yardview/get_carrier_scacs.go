package yardview

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	GetCarrierScacsRequest struct {
		CyclopsYardViewBaseRequest
	}
)

func (y *YardView) GetCarrierScacs(ctx context.Context, source string) ([]models.CyclopsCarrierScacData, error) {
	return y.GetCarrierScacsWithCyclops(ctx, source)
}

func (y *YardView) GetCarrierScacsWithCyclops(
	ctx context.Context,
	source string,
) ([]models.CyclopsCarrierScacData, error) {

	ctx, metaSpan := otel.StartSpan(ctx, "GetCarrierScacsWithCyclops", nil)
	defer func() { metaSpan.End(nil) }()

	cyclopsReq := GetCarrierScacsRequest{
		CyclopsYardViewBaseRequest: CyclopsYardViewBaseRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    YardViewPlatform,
				Action:      models.ActionGetCarrierScacs,
				UserID:      y.scheduler.Username,
				Mode:        models.CyclopsModeAPI,
				Credentials: models.CyclopsCredentials{
					Username: y.creds.Username,
					Password: y.creds.Password,
				},
			},
			Source: source,
		},
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var res models.CyclopsGetCarrierScacsResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return nil, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return nil, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	return res.Scacs, nil
}
