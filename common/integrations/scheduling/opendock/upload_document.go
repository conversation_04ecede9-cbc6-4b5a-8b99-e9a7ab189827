package opendock

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime"
	"mime/multipart"
	"path/filepath"
	"regexp"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

type UploadDocumentResponse struct {
	URL string `json:"url"`
	Key string `json:"key"`
}

func (o *Opendock) UploadDocument(
	ctx context.Context, filename string, fileContent []byte,
) (*UploadDocumentResponse, error) {
	var Response UploadDocumentResponse
	requestBody, contentType, err := createUploadForm(filename, fileContent)
	if err != nil {
		return nil, fmt.Errorf("failed to create upload form: %w", err)
	}

	mimeType := mime.TypeByExtension(filepath.Ext(filename))
	log.Debug(
		ctx,
		"uploading file to Opendock storage",
		zap.String("filename", filename),
		zap.Int("size", len(fileContent)),
		zap.String("mimeType", mimeType),
	)

	err = o.postMultipart(ctx, "storage", requestBody, contentType, &Response)

	if err != nil {
		log.Error(ctx, "failed to upload document to Opendock", zap.Error(err))
		return nil, fmt.Errorf("failed to upload document: %w", err)
	}

	log.Info(ctx, "successfully uploaded file to Opendock storage", zap.String("key", Response.Key))
	return &Response, nil
}

func createUploadForm(filename string, fileContent []byte) (*bytes.Buffer, string, error) {
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	mimeType := mime.TypeByExtension(filepath.Ext(filename))
	if mimeType == "" {
		ext := filepath.Ext(filename)
		switch ext {
		case ".zip":
			mimeType = "application/zip"
		case ".xlsx":
			mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		case ".xls":
			mimeType = "application/vnd.ms-excel"
		case ".csv":
			mimeType = "text/csv"
		case ".jpeg", ".jpg":
			mimeType = "image/jpeg"
		case ".png":
			mimeType = "image/png"
		case ".gif":
			mimeType = "image/gif"
		case ".bmp":
			mimeType = "image/bmp"
		case ".tiff", ".tif":
			mimeType = "image/tiff"
		default:
			mimeType = "application/octet-stream"
		}
	}

	h := make(map[string][]string)
	sanitizedFilename := sanitizeFilenameForHeader(filename)
	h["Content-Disposition"] = []string{fmt.Sprintf(`form-data; name="file"; filename="%s"`, sanitizedFilename)}
	h["Content-Type"] = []string{mimeType}

	fileField, err := writer.CreatePart(h)
	if err != nil {
		return nil, "", fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err = io.Copy(fileField, bytes.NewReader(fileContent)); err != nil {
		return nil, "", fmt.Errorf("failed to copy file content: %w", err)
	}

	contentType := writer.FormDataContentType()
	if err = writer.Close(); err != nil {
		return nil, "", fmt.Errorf("failed to close multipart writer: %w", err)
	}

	return &requestBody, contentType, nil
}

// sanitizeFilenameForHeader sanitizes a filename for safe use in HTTP headers
// by escaping double quotes and removing control characters that could lead to header injection
func sanitizeFilenameForHeader(filename string) string {
	// Remove or replace control characters (ASCII 0-31 and 127)
	controlCharsRegex := regexp.MustCompile(`[\x00-\x1f\x7f]`)
	sanitized := controlCharsRegex.ReplaceAllString(filename, "")

	// Escape double quotes by replacing them with escaped quotes
	sanitized = strings.ReplaceAll(sanitized, `"`, `\"`)

	// Remove carriage returns and line feeds that could break headers
	sanitized = strings.ReplaceAll(sanitized, "\r", "")
	sanitized = strings.ReplaceAll(sanitized, "\n", "")

	// Limit length to prevent excessively long headers
	const maxFilenameLength = 255
	if len(sanitized) > maxFilenameLength {
		// Keep the extension if possible
		ext := filepath.Ext(sanitized)
		if len(ext) < maxFilenameLength-10 {
			base := sanitized[:maxFilenameLength-len(ext)]
			sanitized = base + ext
		} else {
			sanitized = sanitized[:maxFilenameLength]
		}
	}

	return sanitized
}
