package costco

import (
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

const (
	CostcoPlatform = "Costco"
)

type (
	AppointmentData struct {
		AppointmentID   string                       `json:"appointmentId"`
		ProID           string                       `json:"proId"`
		ContainerID     string                       `json:"containerId"`
		TrailerID       string                       `json:"trailerId"`
		TrailerLicense  string                       `json:"trailerLicense"`
		VehicleNumber   string                       `json:"vehicleNumber"`
		VehicleLicense  string                       `json:"vehicleLicense"`
		AppointmentTime string                       `json:"appointmentTime"`
		Duration        int                          `json:"duration"`
		Status          string                       `json:"status"`
		Notes           string                       `json:"notes"`
		Warehouse       *models.CyclopsWarehouseInfo `json:"warehouse,omitempty"`
	}

	AppointmentRequest struct {
		Start             string `json:"start"`
		FreightTrackingID string `json:"freightTrackingId"`
	}

	CyclopsMultipleAppointmentResponse struct {
		models.CyclopsBaseResponse
		Appointments []models.CyclopsAppointmentData `json:"appointments"`
	}
)

// convertToAppointments converts multiple appointment response to a slice of appointments
func convertToAppointments(resp CyclopsMultipleAppointmentResponse) ([]models.Appointment, error) {
	appointments := make([]models.Appointment, 0, len(resp.Appointments))

	for _, apptData := range resp.Appointments {
		var scheduledTime time.Time
		var err error

		// Handle different time formats and unscheduled appointments
		if apptData.ScheduledTime != "" && apptData.ScheduledTime != "-" {
			scheduledTime, err = time.Parse("2006-01-02", apptData.ScheduledTime)
			if err != nil {
				return nil, fmt.Errorf(
					"invalid scheduled time format for appointment %s: %w",
					apptData.AppointmentID,
					err,
				)
			}
		}

		appointment := models.Appointment{
			ExternalID: apptData.AppointmentID,
			StartTime:  scheduledTime,
			Status:     apptData.Status,
			Notes:      apptData.Notes,
		}

		// Add warehouse information if available
		if apptData.Warehouse != nil {
			appointment.WarehouseID = apptData.Warehouse.Name
		}

		appointments = append(appointments, appointment)
	}

	return appointments, nil
}

// convertToAppointmentFromCyclopsAppointment converts a CyclopsAppointment to models.Appointment
func convertToAppointmentFromCyclopsAppointment(apptData models.CyclopsAppointment) (models.Appointment, error) {
	var scheduledTime time.Time
	var err error

	// Handle different time formats and unscheduled appointments
	if apptData.ScheduledTime != "" && apptData.ScheduledTime != "-" {
		scheduledTime, err = time.Parse("2006-01-02T15:04:05", apptData.ScheduledTime)
		if err != nil {
			return models.Appointment{}, fmt.Errorf(
				"invalid scheduled time format for appointment %s: %w",
				apptData.AppointmentID,
				err,
			)
		}
	}

	appointment := models.Appointment{
		ExternalID: apptData.AppointmentID,
		StartTime:  scheduledTime,
		Status:     apptData.Status,
	}

	return appointment, nil
}
