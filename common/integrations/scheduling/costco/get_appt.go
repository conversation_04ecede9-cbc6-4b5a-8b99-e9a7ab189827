package costco

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

func (m *Costco) GetAppointment(ctx context.Context, id string) (models.Appointment, error) {
	return m.GetAppointmentWithCyclops(ctx, id)
}

func (m *Costco) GetAppointmentWithCyclops(ctx context.Context, id string) (models.Appointment, error) {
	req := models.CyclopsGetAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    CostcoPlatform,
			Action:      models.ActionGetAppointment,
			UserID:      m.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: m.creds.Username,
				Password: m.creds.Password,
			},
		},
		Appointment: models.CyclopsAppointmentData{
			AppointmentID: id,
		},
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var res models.CyclopsGetAppointmentResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return models.Appointment{}, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	if len(res.Appointments) < 1 {
		return models.Appointment{}, &models.CyclopsError{
			Message: "no appointments found",
			Errors:  []string{"no appointments found"},
		}
	}

	return convertToAppointmentFromCyclopsAppointment(res.Appointments[0])
}
