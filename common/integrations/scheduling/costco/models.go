package costco

type (
	// Common structures
	ArrivalWindow struct {
		StartDateTime string `json:"startDateTime"`
		Duration      string `json:"duration"`
	}

	NamedEntity struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	}

	Address struct {
		StreetAddress string `json:"streetAddress"`
		Region        string `json:"region"`
		Locality      string `json:"locality"`
		Country       string `json:"country"`
		PostalCode    string `json:"postalCode"`
	}

	// Request/Response structures
	GetAppointmentReq struct {
		Identifiers struct {
			AppointmentID string `json:"appointmentId"`
		} `json:"identifiers"`
	}

	GetAppointmentResp struct {
		Status      string `json:"status"`
		Appointment struct {
			ID            string        `json:"id"`
			ArrivalWindow ArrivalWindow `json:"arrivalWindow"`
			LoadingType   string        `json:"loadingType"`
			DockGroup     NamedEntity   `json:"dockGroup"`
			DockDoor      NamedEntity   `json:"dockDoor"`
		} `json:"appointment"`
		AppointmentConfirmationNumber string  `json:"appointmentConfirmationNumber"`
		AppointmentStatus             string  `json:"appointmentStatus"`
		MostRecentReasonCode          string  `json:"mostRecentReasonCode"`
		MostRecentComment             string  `json:"mostRecentComment"`
		LocationID                    string  `json:"locationId"`
		LocationAddress               Address `json:"locationAddress"`
	}

	GetAvailableApptReq struct {
		Identifiers struct {
			PrimaryReferenceNumber string `json:"primaryReferenceNumber"`
			PoNumber               string `json:"poNumber"`
		} `json:"identifiers"`
	}

	GetAvailableApptResp struct {
		Status       string `json:"status"`
		Appointments []struct {
			ID              string        `json:"id"`
			AppointmentType string        `json:"appointmentType"`
			ArrivalWindow   ArrivalWindow `json:"arrivalWindow"`
			LoadingType     string        `json:"loadingType"`
		} `json:"appointments"`
	}

	MakeAppointmentReq struct {
		PreferredAppointment struct {
			ID string `json:"id"`
		} `json:"preferredAppointment"`
	}

	AppointmentCancelationReq struct {
		Identifiers struct {
			AppointmentID string `json:"appointmentId"`
			ReasonCode    string `json:"reasonCode"`
		} `json:"identifiers"`
	}

	AppointmentCancelationResp struct {
		Status string `json:"status"`
	}
)
