---
description: "Testing guidelines for Go code, especially Redis-related tests"
globs: ["**/*_test.go", "**/testdata/**"]
alwaysApply: true
---

# Testing Guidelines

## Redis Testing

### Mocking Redis:
- Use `redismock` for unit tests
- When mocking `<PERSON><PERSON><PERSON>[T]()`, return JSON-encoded values: `mock.SetVal(`"value"`)` not `mock.SetVal("value")`
- For integration tests that need Redis disabled, use `DISABLE_RATE_LIMIT=true` environment variable

### Example Test Setup:
```go
func TestMain(m *testing.M) {
    // Disable rate limiting for tests to avoid Redis connection issues
    os.Setenv("DISABLE_RATE_LIMIT", "true")
    exitCode := m.Run()
    os.Unsetenv("DISABLE_RATE_LIMIT")
    os.Exit(exitCode)
}
```

### Redis Mock Examples:
```go
// ✅ Correct - JSON-encoded value for <PERSON><PERSON><PERSON>[string]
mock.ExpectGet("key").SetVal(`"myvalue"`)

// ❌ Wrong - raw string will cause JSON unmarshal error
mock.ExpectGet("key").SetVal("myvalue")
```

## General Testing Best Practices

- Write meaningful test names that describe the scenario
- Use table-driven tests for multiple similar test cases
- Test both success and error paths
- Use `testify/assert` and `testify/require` appropriately
- Mock external dependencies
- Keep tests focused and independent