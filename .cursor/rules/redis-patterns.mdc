---
description: "Enforce Redis wrapper functions and prevent direct Redis client access"
globs: ["**/*.go"]
alwaysApply: true
---

# Redis Access Patterns

## CRITICAL: Always Use Redis Wrapper Functions

**NEVER use direct Redis client access. Always use the wrapper functions from `common/redis`.**

### ❌ FORBIDDEN Patterns:
```go
// NEVER do this - direct Redis access
redis.RDB.Get(ctx, key)
redis.RDB.Set(ctx, key, value, expiration)
redis.RDB.Del(ctx, key)
redis.RDB.Exists(ctx, key)
redis.RDB.Incr(ctx, key)
```

### ✅ REQUIRED Patterns:
```go
// ALWAYS use wrapper functions
import "github.com/drumkitai/drumkit/common/redis"

// For getting values with automatic JSON unmarshaling
result, found, err := redis.GetKey[string](ctx, key)
result, found, err := redis.GetKey[MyStruct](ctx, key)

// For setting values with automatic JSON marshaling
err := redis.SetKey(ctx, key, value, expiration)

// For deleting keys
err := redis.DeleteKey(ctx, key)

// For incrementing counters
count, err := redis.IncrementKey(ctx, key, expiration)

// For checking existence
exists, err := redis.KeyExists(ctx, key)
```

### Why This Rule Exists:
1. **Consistency**: Ensures all Redis access uses the same JSON marshaling/unmarshaling
2. **Error Handling**: Centralized Redis error handling and logging
3. **Bug Prevention**: Prevents marshaling mismatches that cause silent failures
4. **Maintainability**: Changes to Redis behavior can be made in one place

### Exception Handling:
- If wrapper functions don't support your use case, **extend the wrapper functions** rather than using direct access
- For complex Redis operations (pipelines, transactions), discuss with the team first