package main

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	retalixPollerKey = "retalix_poller_last_run"
	// Slightly more than 7 days to ensure key remains available for the next week's execution
	retalixPollerTTL = 7*24*time.Hour + 1*time.Hour
)

// shouldRunRetalixPoller checks if the Retalix warehouse poller should run
// It uses Redis to track the last run time and ensures it only runs once per week
func shouldRunRetalixPoller(ctx context.Context) bool {
	lastRun, found, err := redis.GetKey[string](ctx, retalixPollerKey)
	if err != nil {
		log.WarnNoSentry(ctx, "could not get last Retalix warehouse poller run time, will run", zap.Error(err))
		return true
	}

	if !found || lastRun == "" {
		log.Info(ctx, "no previous Retalix poller run recorded, will run")
		return true
	}

	lastRunTime, err := time.Parse(time.RFC3339, lastRun)
	if err != nil {
		log.WarnNoSentry(ctx, "could not parse last Retalix warehouse poller run time, will run", zap.Error(err))
		return true
	}

	return time.Since(lastRunTime) >= 7*24*time.Hour
}

func updateRetalixPollerLastRun(ctx context.Context) error {
	now := time.Now().Format(time.RFC3339)
	return redis.SetKey(ctx, retalixPollerKey, now, retalixPollerTTL)
}

// pollRetalixWarehouses polls warehouses from Retalix integrations and syncs them with the database.
// This function should be called once a week to keep warehouse data up to date.
// It processes the internal Retalix integration in a single run to avoid duplicate work.
func pollRetalixWarehouses(ctx context.Context) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "pollRetalixWarehouses", nil)
	defer func() {
		if err != nil {
			log.Error(ctx, "Retalix warehouse polling job failed", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	internalRetalix, err := integrationDB.GetInternalSchedulerByName(ctx, models.Retalix)
	if err != nil {
		return fmt.Errorf("error getting Retalix integration: %w", err)
	}

	log.Info(ctx, "found internal Retalix integration", zap.String("account", internalRetalix.Username))

	if err := updateWarehouseDBFromRetalix(ctx, internalRetalix); err != nil {
		return fmt.Errorf("error polling internal Retalix integration: %w", err)
	}

	log.Info(ctx, "completed Retalix warehouse polling")
	return nil
}

// updateWarehouseDBFromRetalix handles warehouse sync using an internal Retalix integration
func updateWarehouseDBFromRetalix(ctx context.Context, integration models.Integration) error {
	ctx, span := otel.StartSpan(ctx, "updateWarehouseDBFromRetalix", nil)
	defer span.End(nil)

	log.Info(ctx, "polling with internal Retalix integration")

	// Collect warehouses from the internal integration
	var retalixWarehouses []models.Warehouse

	client, err := scheduling.New(ctx, integration)
	if err != nil {
		log.Error(
			ctx,
			"error creating Retalix client",
			zap.Uint("integrationID", integration.ID),
			zap.Error(err),
		)
	}

	warehouses, err := client.GetAllWarehouses(ctx)
	if err != nil {
		log.Error(
			ctx,
			"error getting warehouses from Retalix",
			zap.Uint("integrationID", integration.ID),
			zap.Error(err),
		)
	}

	log.Info(
		ctx,
		"retrieved warehouses from Retalix integration",
		zap.Int("warehouseCount", len(warehouses)),
		zap.Uint("integrationID", integration.ID),
	)

	retalixWarehouses = warehouses

	if len(retalixWarehouses) == 0 {
		return errors.New("failed to get warehouses from Retalix integration")
	}

	// Get all existing warehouses from database
	existingWarehouses, err := warehouseDB.GetWarehousesBySource(ctx, models.RetalixSource)
	if err != nil {
		return fmt.Errorf("error getting existing Retalix warehouses from database: %w", err)
	}

	log.Info(
		ctx,
		"retrieved existing Retalix warehouses from database",
		zap.Int("warehouseCount", len(existingWarehouses)),
	)

	// Create maps for efficient lookup
	retalixWarehouseMap := make(map[string]models.Warehouse)
	for _, wh := range retalixWarehouses {
		retalixWarehouseMap[wh.WarehouseID] = wh
	}

	existingWarehouseMap := make(map[string]models.Warehouse)
	for _, wh := range existingWarehouses {
		existingWarehouseMap[wh.WarehouseID] = wh
	}

	// Find warehouses to create (in Retalix but not in DB)
	var warehousesToCreate []models.Warehouse
	for _, wh := range retalixWarehouses {
		if _, exists := existingWarehouseMap[wh.WarehouseID]; !exists {
			warehousesToCreate = append(warehousesToCreate, wh)
		}
	}

	// Find warehouses to soft delete (in DB but not in Retalix)
	var warehousesToDelete []models.Warehouse
	for _, wh := range existingWarehouses {
		if _, exists := retalixWarehouseMap[wh.WarehouseID]; !exists {
			warehousesToDelete = append(warehousesToDelete, wh)
		}
	}

	log.Info(
		ctx,
		"retalix warehouse sync analysis complete",
		zap.Int("toCreate", len(warehousesToCreate)),
		zap.Int("toDelete", len(warehousesToDelete)),
		zap.Int("totalRetalixWarehouses", len(retalixWarehouses)),
		zap.Int("totalExistingWarehouses", len(existingWarehouses)),
	)

	// Create new warehouses
	if len(warehousesToCreate) > 0 {
		if err := warehouseDB.OnboardWarehouses(ctx, warehousesToCreate); err != nil {
			return fmt.Errorf("error creating Retalix warehouses: %w", err)
		}
		log.Info(ctx, "created Retalix warehouses", zap.Int("count", len(warehousesToCreate)))
	}

	// Soft delete warehouses that no longer exist in Retalix
	if len(warehousesToDelete) > 0 {
		if err := softDeleteWarehousesInDB(ctx, warehousesToDelete); err != nil {
			return fmt.Errorf("error soft deleting Retalix warehouses: %w", err)
		}
		log.Info(ctx, "soft deleted Retalix warehouses", zap.Int("count", len(warehousesToDelete)))
	}

	return nil
}
