package main

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	opendockPollerKey = "opendock_poller_last_run"
	// Slightly more than 24 hours to ensure key remains available for next day's execution
	opendockPollerTTL = 25 * time.Hour
)

// shouldRunOpendockPoller checks if the Opendock warehouse poller should run
// It uses Redis to track the last run time and ensures it only runs once per day
func shouldRunOpendockPoller(ctx context.Context) bool {
	lastRun, found, err := redis.GetKey[string](ctx, opendockPollerKey)
	if err != nil {
		log.WarnNoSentry(ctx, "could not get last opendock warehouse poller run time, will run", zap.Error(err))
		return true
	}

	if !found || lastRun == "" {
		log.Info(ctx, "no previous opendock poller run recorded, will run")
		return true
	}

	lastRunTime, err := time.Parse(time.RFC3339, lastRun)
	if err != nil {
		log.WarnNoSentry(ctx, "could not parse last opendock warehouse poller run time, will run", zap.Error(err))
		return true
	}

	return time.Since(lastRunTime) >= 24*time.Hour
}

func updateOpendockPollerLastRun(ctx context.Context) error {
	now := time.Now().Format(time.RFC3339)
	return redis.SetKey(ctx, opendockPollerKey, now, opendockPollerTTL)
}

// pollOpendockWarehouses polls warehouses from Opendock integrations and syncs them with the database.
// This function should be called once a day to keep warehouse data up to date.
// It processes all Opendock integrations in a single run to avoid duplicate work.
func pollOpendockWarehouses(ctx context.Context) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "pollOpendockWarehouses", nil)
	defer func() {
		if err != nil {
			log.Error(ctx, "Opendock warehouse polling job failed", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	internalOpendock, err := integrationDB.GetInternalOpendockForPoller(ctx)
	if err != nil {
		return fmt.Errorf("error getting Opendock integrations: %w", err)
	}

	log.Info(ctx, "found internal Opendock integration", zap.String("account", internalOpendock.Username))

	if err := updateWarehouseDBFromOpendock(ctx, internalOpendock); err != nil {
		return fmt.Errorf("error polling first valid Opendock integration: %w", err)
	}

	log.Info(ctx, "completed Opendock warehouse polling")
	return nil
}

// updateWarehouseDBFromOpendock handles warehouse sync using an internal Opendock integration
func updateWarehouseDBFromOpendock(ctx context.Context, integration models.Integration) error {
	ctx, span := otel.StartSpan(ctx, "updateWarehouseDBFromOpendock", nil)
	defer span.End(nil)

	log.Info(ctx, "polling with internal Opendock integration")

	// Collect warehouses from the first valid integration
	var opendockWarehouses []models.Warehouse

	client, err := scheduling.New(ctx, integration)
	if err != nil {
		log.Error(
			ctx,
			"error creating Opendock client",
			zap.Uint("integrationID", integration.ID),
			zap.Error(err),
		)
	}

	warehouses, err := client.GetAllWarehouses(ctx)
	if err != nil {
		log.Error(
			ctx,
			"error getting warehouses from Opendock",
			zap.Uint("integrationID", integration.ID),
			zap.Error(err),
		)
	}

	log.Info(
		ctx,
		"retrieved warehouses from Opendock integration",
		zap.Int("warehouseCount", len(warehouses)),
		zap.Uint("integrationID", integration.ID),
	)

	opendockWarehouses = warehouses

	if len(opendockWarehouses) == 0 {
		return errors.New("failed to get warehouses from Opendock integration")
	}

	// Get all existing warehouses from database
	existingWarehouses, err := warehouseDB.GetWarehousesBySource(ctx, models.OpendockSource)
	if err != nil {
		return fmt.Errorf("error getting existing warehouses from database: %w", err)
	}

	log.Info(
		ctx,
		"retrieved existing warehouses from database",
		zap.Int("warehouseCount", len(existingWarehouses)),
	)

	// Create maps for efficient lookup
	opendockWarehouseMap := make(map[string]models.Warehouse)
	for _, wh := range opendockWarehouses {
		opendockWarehouseMap[wh.WarehouseID] = wh
	}

	existingWarehouseMap := make(map[string]models.Warehouse)
	for _, wh := range existingWarehouses {
		existingWarehouseMap[wh.WarehouseID] = wh
	}

	// Find warehouses to create (in Opendock but not in DB)
	var warehousesToCreate []models.Warehouse
	for _, wh := range opendockWarehouses {
		if _, exists := existingWarehouseMap[wh.WarehouseID]; !exists {
			warehousesToCreate = append(warehousesToCreate, wh)
		}
	}

	// Find warehouses to soft delete (in DB but not in Opendock)
	var warehousesToDelete []models.Warehouse
	for _, wh := range existingWarehouses {
		if _, exists := opendockWarehouseMap[wh.WarehouseID]; !exists {
			warehousesToDelete = append(warehousesToDelete, wh)
		}
	}

	log.Info(
		ctx,
		"warehouse sync analysis complete",
		zap.Int("toCreate", len(warehousesToCreate)),
		zap.Int("toDelete", len(warehousesToDelete)),
		zap.Int("totalOpendockWarehouses", len(opendockWarehouses)),
		zap.Int("totalExistingWarehouses", len(existingWarehouses)),
	)

	// Create new warehouses
	if len(warehousesToCreate) > 0 {
		if err := warehouseDB.OnboardWarehouses(ctx, warehousesToCreate); err != nil {
			return fmt.Errorf("error creating warehouses: %w", err)
		}
		log.Info(ctx, "created warehouses", zap.Int("count", len(warehousesToCreate)))
	}

	// Soft delete warehouses that no longer exist in Opendock
	if len(warehousesToDelete) > 0 {
		if err := softDeleteWarehousesInDB(ctx, warehousesToDelete); err != nil {
			return fmt.Errorf("error soft deleting warehouses: %w", err)
		}
		log.Info(ctx, "soft deleted warehouses", zap.Int("count", len(warehousesToDelete)))
	}

	return nil
}

func softDeleteWarehousesInDB(ctx context.Context, warehouses []models.Warehouse) error {
	if len(warehouses) == 0 {
		return nil
	}

	warehouseIDs := make([]uint, len(warehouses))
	for i, wh := range warehouses {
		warehouseIDs[i] = wh.ID
	}

	err := warehouseDB.SoftDeleteByIDs(ctx, warehouseIDs)
	if err != nil {
		return fmt.Errorf("error soft deleting warehouses in DB: %w", err)
	}

	return nil
}
