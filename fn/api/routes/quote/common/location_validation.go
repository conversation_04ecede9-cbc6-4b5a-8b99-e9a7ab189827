package quote

import (
	"context"
	"errors"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/service/location"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/fn/api/env"
)

// Error message constants for location validation
const (
	ErrInvalidLocation  = "invalid location"
	ErrInvalidZip       = "invalid zip code"
	ErrInvalidCityState = "invalid city/state"
)

// LocationValidator holds configuration for location validation
type LocationValidator struct {
	awsLookupZipFunc func(
		ctx context.Context,
		city, state string,
	) (string, error)

	uspsLookupCityStateFunc func(
		ctx context.Context,
		zip, userID string,
	) (helpers.CityStateLookupResponse, error)

	canadaLocationFunc func(
		ctx context.Context,
		postalCode *string,
	) (city, province string, err error)

	awsLocationFunc func(
		ctx context.Context,
		city, state, zip string,
	) (*location.SearchPlaceIndexForTextOutput, error)
}

// LocationValidatorOption is a functional option for configuring LocationValidator
// Primarily for unit testing, unlikely to be used in production
type LocationValidatorOption func(*LocationValidator)

// WithAwsLookupZipFunc sets the zip lookup function
func WithAwsLookupZipFunc(fn func(ctx context.Context, city, state string) (string, error)) LocationValidatorOption {
	return func(v *LocationValidator) {
		v.awsLookupZipFunc = fn
	}
}

// WithUspsLookupCityStateFunc sets the city/state lookup function
func WithUspsLookupCityStateFunc(
	fn func(ctx context.Context, zip, userID string) (helpers.CityStateLookupResponse, error),
) LocationValidatorOption {

	return func(v *LocationValidator) {
		v.uspsLookupCityStateFunc = fn
	}
}

// WithCanadaLocationFunc sets the Canada location lookup function
func WithCanadaLocationFunc(
	fn func(ctx context.Context, postalCode *string) (city, province string, err error),
) LocationValidatorOption {

	return func(v *LocationValidator) {
		v.canadaLocationFunc = fn
	}
}

// WithAWSLocationFunc sets the AWS location lookup function
func WithAWSLocationFunc(
	fn func(ctx context.Context, city, state, zip string) (*location.SearchPlaceIndexForTextOutput, error),
) LocationValidatorOption {

	return func(v *LocationValidator) {
		v.awsLocationFunc = fn
	}
}

// NewLocationValidator creates a new LocationValidator with the given options
func NewLocationValidator(opts ...LocationValidatorOption) *LocationValidator {
	v := &LocationValidator{
		awsLookupZipFunc:        helpers.LookupZipCodeByCityState,
		uspsLookupCityStateFunc: helpers.LookupCityStateByZipcode,
		awsLocationFunc:         helpers.AwsLocationLookup,
	}

	// Set the Canada location function with the AWS function dependency
	v.canadaLocationFunc = func(ctx context.Context, postalCode *string) (city, province string, err error) {
		return getCanadaLocationByPostalCode(ctx, postalCode, v.awsLocationFunc)
	}

	for _, opt := range opts {
		opt(v)
	}

	return v
}

// ValidateLocation validates and optionally fills in missing location information
func (v *LocationValidator) ValidateLocation(
	ctx context.Context,
	city, state, zip *string,
	country *CountryName,
) error {

	if city == nil || state == nil || zip == nil {
		return errors.New("city, state, and zip must not be nil")
	}

	// Lookup location by zip if city/state is blank
	if helpers.IsBlank(*city) || helpers.IsBlank(*state) {
		if country != nil && *country == CountryCanada {

			newCity, newProvince, err := v.canadaLocationFunc(ctx, zip)
			if err != nil {
				return fmt.Errorf("error mapping canada postal code to city/state: %w", err)
			}

			*city = newCity
			*state = newProvince
			*country = CountryCanada

			return nil
		}

		// Lookup city/state by zip via USPS API
		cityState, err := v.uspsLookupCityStateFunc(ctx, *zip, env.Vars.USPSUserID)
		if err != nil {
			// USPS returns "Invalid Zip Code" if zip is invalid
			return fmt.Errorf("error mapping zip to city/state: %w", err)
		}

		*city = cityState.ZipCode.City
		*state = cityState.ZipCode.State
		if country != nil {
			*country = CountryUS
		}

		return nil
	}

	// Lookup city/state by zip if zip is blank
	if helpers.IsBlank(*zip) {
		// Lookup zip by city/state via AWS which covers both countries
		zipResult, err := v.awsLookupZipFunc(ctx, *city, *state)
		if err != nil {
			return fmt.Errorf("error mapping city/state to zip: %w", err)
		}

		if len(zipResult) < 3 {
			return fmt.Errorf("no valid zips found for city/state %s, %s: %s", *city, *state, zipResult)
		}

		*zip = zipResult
		if len(zipResult) == 5 {
			*country = CountryUS
		} else {
			*country = CountryCanada
		}
	}

	return nil
}

// ValidateLocation is a convenience function that uses default lookup functions
func ValidateLocation(ctx context.Context, city, state, zip *string, country *CountryName) error {
	validator := NewLocationValidator()
	return validator.ValidateLocation(ctx, city, state, zip, country)
}

func getCanadaLocationByPostalCode(
	ctx context.Context,
	postalCode *string,
	awsLocationFunc func(ctx context.Context, city, state, zip string) (*location.SearchPlaceIndexForTextOutput, error),
) (string, string, error) {

	// Using AWS Locations for Canada postal codes, since they're not supported by USPS API
	pickupLocation, err := awsLocationFunc(ctx, "", "", *postalCode)
	if err != nil || pickupLocation == nil {
		if err == nil && pickupLocation == nil {
			err = errors.New("failed to lookup location: no response from AWS Location service")
		}

		return "", "", err
	}

	if len(pickupLocation.Results) == 0 || pickupLocation.Results[0].Place == nil {
		return "", "", fmt.Errorf("invalid zip code: %s", *postalCode)
	}

	pickupPlace := *pickupLocation.Results[0].Place

	city := ""
	province := ""

	if pickupPlace.Region != nil {
		province = *pickupPlace.Region
	}

	if len(province) > 2 {
		provinceAbbr, err := helpers.GetCanadaProvinceAbbreviation(province)
		if err != nil {
			return "", "", err
		}

		province = provinceAbbr
	}

	if pickupPlace.Municipality != nil {
		city = *pickupPlace.Municipality
	}

	return city, province, nil
}
