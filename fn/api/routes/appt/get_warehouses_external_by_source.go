package appt

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	GetWarehousesExternalBySourceQuery struct {
		Source        models.WarehouseSource `json:"source" validate:"required"`
		RequestSource string                 `json:"requestSource" validate:"required"`
		IntegrationID uint                   `json:"integrationID"`
	}

	GetWarehousesExternalBySourceResponse struct {
		Warehouses []models.WarehouseCore `json:"warehouses"`
	}
)

// NOTE: Only YardView uses this now
func GetWarehousesExternalBySource(c *fiber.Ctx) error {
	var query GetWarehousesExternalBySourceQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))

	userID := middleware.UserIDFromContext(c)
	userServiceID := middleware.ServiceIDFromContext(c)

	log.Info(ctx, "fetching warehouses", zap.String("source", string(query.Source)))

	resp, err := getWarehousesExternalBySource(ctx, userID, userServiceID, query.Source, query.RequestSource, query.IntegrationID)
	if err != nil {
		log.Error(ctx, "getWarehouse failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	log.Debug(ctx, "warehouses", zap.Any("warehouses", resp))

	return c.Status(http.StatusOK).JSON(resp)
}

func getWarehousesExternalBySource(
	ctx context.Context,
	userID uint,
	serviceID uint,
	source models.WarehouseSource,
	requestSource string,
	integrationID uint,
) (_ *GetWarehousesExternalBySourceResponse, err error) {

	ctx, span := otel.StartSpan(ctx, "getWarehousesExternalBySource", nil)
	defer func() { span.End(err) }()

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		userID,
		serviceID,
		string(source),
		integrationID,
	)
	if err != nil {
		return nil, fmt.Errorf("fetch integration error: %w", err)
	}

	ctx = log.With(
		ctx,
		zap.String("schedulingUsername", integration.Username),
		zap.Uint("integrationID", integration.ID),
	)

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		return nil, fmt.Errorf("create scheduler client: %w", err)
	}

	warehouse := models.Warehouse{
		WarehouseID: requestSource,
	}

	warehouses, err := client.GetAllWarehouses(ctx, models.WithWarehouse(warehouse))
	if err != nil {
		log.Error(ctx, "error fetching getWarehouses", zap.Error(err))
		return nil, fmt.Errorf("error getting warehouses for integration: %w", err)
	}

	var warehousesResult []models.WarehouseCore
	for _, wh := range warehouses {
		warehousesResult = append(warehousesResult, apiutil.GetWarehouseCoreFromWarehouse(wh))
	}

	return &GetWarehousesExternalBySourceResponse{
		Warehouses: warehousesResult,
	}, nil
}
